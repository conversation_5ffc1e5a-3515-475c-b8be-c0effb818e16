// ==UserScript==
// @name                 Edewakaru Enhanced
// @name:en              「絵でわかる日本語」 Reading Experience Enhancement
// @name:ja              「絵でわかる日本語」 閲覧体験強化
// @name:zh-CN           「絵でわかる日本語」 阅读体验增强
// @name:zh-TW           「絵でわかる日本語」 閱讀體驗增強
// @name:ko              「絵でわかる日本語」 독서 경험 향상
// @name:vi              「絵でわかる日本語」 Nâng Cao Trải Nghiệm Đọc
// @namespace            https://greasyfork.org/users/49949-ipumpkin
// @version              2025.07.13
// <AUTHOR>
// @description:en       Enhances reading experience on the "絵でわかる日本語" site by converting kanji readings from parentheses to ruby, hiding ads and clutter, and adding text-to-speech for selected text.
// @description:ja       「絵でわかる日本語」サイト内の漢字の読みを括弧表記から自動でふりがなに変換し、広告や不要な要素を非表示にします。選択テキストの読み上げ機能にも対応し、快適な読書体験を実現します。
// @description:zh-CN    将「絵でわかる日本語」网站中的汉字注音由括号形式自动转换为振假名，隐藏广告和无关元素，并支持划词朗读功能，提升阅读体验。
// @description:zh-TW    將「絵でわかる日本語」網站中的漢字注音由括號形式自動轉換為振假名，隱藏廣告與無關元素，並支援劃詞朗讀功能，提升閱讀體驗。
// @description:ko       「絵でわかる日本語」웹사이트에서 한자 읽기를 괄호 표기에서 루비로 변환하고, 광고 및 불필요한 요소를 숨기며, 선택한 텍스트에 대한 텍스트 음성 변환 기능을 추가하여 읽기 경험을 향상시킵니다。
// @description:vi       Cải thiện trải nghiệm đọc trên trang web「絵でわかる日本語」bằng cách chuyển đổi cách đọc kanji từ dấu ngoặc đơn thành ruby, ẩn quảng cáo và các yếu tố không cần thiết, đồng thời thêm tính năng text-to-speech cho văn bản được chọn.
// @license              GPL-3.0
// @icon                 https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// @match                https://www.edewakaru.com/*
// @grant                GM_addStyle
// @grant                GM_getValue
// @grant                GM_setValue
// @run-at               document-start
// ==/UserScript==

;(function () {
  ;('use strict')

  // ===================================================================================
  // C O N F I G U R A T I O N
  // 集中存放所有需要便捷修改的数据
  // ===================================================================================
  const RULES = {
    // 文本规则处理说明：
    // 以下规则在纯文本节点上运行，是注音转换的核心。
    //
    // 主要分为三类：
    // 1. MANUAL_MARK:
    //    - 用途: 强制标记单词边界，用于自动分词可能出错的特例。
    //    - 格式: '汉字（读音）' 格式的字符串。
    //    - 工作流程: 脚本会精确匹配并转换为 <ruby>。
    //
    // 2. FULL_REPLACE:
    //    - 用途: 完全手动控制最终的 HTML，用于最复杂的场景。
    //    - 格式: { pattern: '模式（任意内容）', replacement: '最终HTML字符串' }。
    //    - 工作流程: 直接将匹配 pattern 的内容替换为 replacement 字符串。

    // 文本层处理规则 (在纯文本节点上操作)
    TEXT: {
      // 手动标记：强制分割，作用是标记单词边界（普通词能自动匹配，此为例外）
      MANUAL_MARK: [
        'km（キロ）',
        'm（メートル）',
        '℃（ど）',
        'お団子（おだんご）',
        'お年寄り（おとしより）',
        'お店（おみせや）',
        'お茶する（おちゃする）',
        'ご先祖さま（ごせんぞさま）',
        '一つ（ひとつ）',
        '万引き（まんびき）',
        '三分の一（さんぶんのいち）',
        '不確か（ふたしか）',
        '不足（ふそく）',
        '世界１周旅行（せかいいっしゅうりょこう）',
        '中（じゅう）',
        '以上（いじょう）',
        '以外（いがい）',
        '住（す）',
        '使い分け（つかいわけ）',
        '使い方（つかいかた）',
        '使用（しよう）',
        '働（はたら）',
        '元を取る（もとをとる）',
        '元カノ（もとかの）',
        '元カレ（もとかれ）',
        '入学（にゅうがく）',
        '入（はい）',
        '全て（すべて）',
        '出張（しゅっちょう）',
        '分（ぶん）',
        '前（まえ）',
        '動作（どうさ）',
        '口の中（くちのなか）',
        '合（あ）',
        '吐き気（はきけ）',
        '味覚 （みかく）',
        '呼び方（よびかた）',
        '唐揚げ（からあげ）',
        '商品（しょうひん）',
        '土砂崩れ（どしゃくずれ）',
        '夏休み中（なつやすみちゅう）',
        '夏祭り（なつまつり）',
        '夕ご飯（ゆうごはん）',
        '大切（たいせつ）',
        '大好き（だいすき）',
        '学習者（がくしゅうしゃ）',
        '宝くじ（たからくじ）',
        '寝る前（ねるまえ）',
        '寝（ね）',
        '届け出（とどけで）',
        '座り心地（すわりごこち）',
        '引っ越す（ひっこす）',
        '当たり前（あたりまえ）',
        '役に立つ（やくにたつ）',
        '待（ま）',
        '後ろ（うしろ）',
        '怒り（いかり）',
        '思い出す（おもいだす）',
        '恵方巻き（えほうまき）',
        '悩み事（なやみごと）',
        '感じ方（かんじかた）',
        '戦（せん）',
        '手作り（てづくり）',
        '折があれば（おりがあれば）',
        '折に触れて（おりにふれて）',
        '折も折（おりもおり）',
        '折を見て（おりをみて）',
        '数え方（かぞえかた）',
        '文化（ぶんか）',
        '文法（ぶんぽう）',
        '旅行（りょこう）',
        '日記（にっき）',
        '早寝早起き（はやねはやおき）',
        '星の数ほどある（ほしのかずほどある）',
        '星の数ほどいる（ほしのかずほどいる）',
        '星の数（ほしのかず）',
        '昭和の日（しょうわのひ）',
        '暮（ぐ）',
        '有名（ゆうめい）',
        '梅雨入り（つゆいり）',
        '楽（たの）',
        '歩（ある）',
        '残業（ざんぎょう）',
        '気を付けて（きをつけて）',
        '気持ち（きもち）',
        '独り言（ひとりごと）',
        '瓜二つ（うりふたつ）',
        '甘い物（あまいもの）',
        '申し訳（もうしわけ）',
        '盗み食い（ぬすみぐい）',
        '真っ暗（まっくら）',
        '真ん中（まんなか）',
        '知り合い（しりあい）',
        '確か（たしか）',
        '社会（しゃかい）',
        '福笑い（ふくわらい）',
        '窓の外（まどのそと）',
        '立ち読み（たちよみ）',
        '第２月曜日（だいにげつようび）',
        '笹の葉（ささのは）',
        '細長い（ほそながい）',
        '紹介（しょうかい）',
        '組み合わせ（くみあわせ）',
        '経（た）',
        '結婚（けっこん）',
        '繰り返して（くりかえして）',
        '羽根つき（はねつき）',
        '考え方（かんがえかた）',
        '腹が立つ（はらがたつ）',
        '自身（じしん）',
        '芸術の秋（げいじゅつのあき）',
        '落ち着（おちつ）',
        '行き方（いきかた）',
        '行き渡る（いきわたる）',
        '触り心地（さわりごこち）',
        '試験（しけん）',
        '話し手（はなして）',
        '話し言葉（はなしことば）',
        '読み方（よみかた）',
        '読書の秋（どくしょのあき）',
        '請け合い（うけあい）',
        '豪雨（ごうう）',
        '貯金（ちょきん）',
        '貯（た）',
        '買い物（かいもの）',
        '貸し借り（かしかり）',
        '足が早い（あしがはやい）',
        '通り（とおり）',
        '通り（どおり）',
        '通知（つうち）',
        '通（どお）',
        '連続（れんぞく）',
        '遅刻（ちこく）',
        '長い間（ながいあいだ）',
        '長生き（ながいき）',
        '雨の日（あめのひ）',
        '青い色（あおいいろ）',
        '青のり（あおのり）',
        '願い事（ねがいごと）',
        '食べず嫌い（たべずぎらい）',
        '食べ物（たべもの）',
        '食欲の秋（しょくよくのあき）',
        '食（しょく）',
        '飲み会（のみかい）',
        '飲み物（のみもの）',
        '駅（えき）',
        '驚き（おどろき）',
        '髪の毛（かみのけ）',
        '鳴き声（なきごえ）',
        '０点（れいてん）',
        '１か月間（いっかげつかん）',
        '１か月（いっかげつ）',
        '１つ（ひとつ）',
        '１人（ひとり）',
        '１列（いちれつ）',
        '１回（いっかい）',
        '１年（いちねん）',
        '１度（いちど）',
        '１日中（いちにちじゅう）',
        '１日（ついたち）',
        '１杯（いっぱい）',
        '１泊（いっぱく）',
        '１０日間（とおかかん）',
        '１０日（とおか）',
        '１０杯（じゅっぱい）',
        '２人（ふたり）',
        '２日（ふつか）',
        '３日間（みっかかん）',
        '３日（みっか）',
        '３杯（さんばい）',
        '５分（ごふん）',
        '５日間（いつかかん）',
        '５月（ごがつ）',
        '７日（なのか）',
        '夏向き（なつむき）',
        '持ち家（もちいえ）',
        '割り箸（わりばし）',
        '５日前（いつかまえ）',
        '震える（ふるえる）',
        '並み（なみ）',
        'お茶（おちゃ）',
        '１番（いちばん）',
        '中旬（ちゅうじゅん）',
        'コマ回し（こままわし）',
        '青リンゴ（あおりんご）',
        '夏バテ防止（なつばてぼうし）',
        'コマ回し（こままわし）',
        '青リンゴ（あおりんご）',
        '夏バテ防止（なつばてぼうし）',
        '引っ越し（ひっこし）',
      ],

      // 全替换：提供模式和最终的 HTML，用于最复杂的送假名等情况
      FULL_REPLACE: [
        { pattern: '羽根を伸ばす（羽根を伸ばす）', replacement: '羽根を伸ばす（はねをのばす）' },
        { pattern: '長蛇の列（長蛇の列）', replacement: '長蛇の列（ちょうだのれつ）' },
        { pattern: '食べ物（食べ物）', replacement: '食べ物（たべもの）' },
        { pattern: '今回（今回）', replacement: '今回（こんかい）' },
        { pattern: '一般的（いっぱん）', replacement: '一般的（いっぱんてき）' },
        { pattern: '付き合（つきあい）', replacement: '付き合（つきあ）' },
        { pattern: '必ず（かなら）', replacement: '必ず（かならず）' },
        { pattern: '恥（はず）', replacement: '恥（は）' },
        { pattern: '何（なに・なん）', replacement: '何（なん）' },
        { pattern: '耳が痛い（みみがいたい）', replacement: '耳（みみ）が痛（いた）い' },
        { pattern: 'マイ〇〇（my+〇〇）', replacement: '<ruby>マイ<rt>my</rt></ruby>〇〇' },
        { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
        { pattern: '聞き手（ききて）', replacement: '<ruby>聞<rt>き</rt></ruby>き<ruby>手<rt>て</rt></ruby>' },
        { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
        { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
        { pattern: '年越しそば（としこしそば）', replacement: '<ruby>年越<rt>としこ</rt></ruby>しそば' },
        { pattern: '顔から火が出る（かおからひがでる）', replacement: '<ruby>顔<rt>かお</rt></ruby>から<ruby>火<rt>ひ</rt></ruby>が<ruby>出<rt>で</rt></ruby>る' },
        { pattern: '原因・理由（げんいん・りゆう）', replacement: '<ruby>原因<rt>げんいん</rt></ruby>・<ruby>理由<rt>りゆう</rt></ruby>' },
        { pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）', replacement: '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える' },
        { pattern: '青菜・青野菜（あおな・あおやさい）', replacement: '<ruby>青菜<rt>あおな</rt></ruby>・<ruby>青野菜<rt>あおやさい</rt></ruby>' },
        { pattern: '水の泡になる・水の泡となる（みずのあわになる）', replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる' },
        { pattern: '意味で（いみ）', replacement: '<ruby>意味<rt>いみ</rt></ruby>で' },
        { pattern: '和製英語で（わせいえいご）', replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で' },
        { pattern: '財布を（さいふ）', replacement: '<ruby>財布<rt>さいふ</rt></ruby>を' },
        { pattern: 'ソーシャル・ネットワーキング・サービス（Social Networking Service）', replacement: '<ruby>ソーシャル<rt>Social</rt></ruby>・<ruby>ネットワーキング<rt>Networking</rt></ruby>・<ruby>サービス<rt>Service</rt></ruby>' },
      ],
    },

    // HTML 规则处理说明：
    // 以下规则在文本处理引擎之前运行，直接对元素的 innerHTML 进行操作。
    // 这允许我们预处理复杂的、跨越多个 HTML 标签的模式，将其简化为后续文本引擎能够理解的格式。
    //
    // 主要分为三类：
    // 1. MANUAL_MARK:
    //    - 用途: 简化那些因包含 HTML 标签而难以直接在文本层处理的简单注音。
    //    - 格式: 一个包含 HTML 的字符串，例如 '甘み</b>（あまみ）'。
    //    - 工作流程: 脚本会自动移除所有 HTML 标签，将结果（例如 '甘み（あまみ）'）交给后续的文本引擎处理。
    //
    // 2. FULL_REPLACE:
    //    - 用途: 提供最大的灵活性，用于完全手动的、端到端的替换。
    //    - 格式: { pattern: '字符串或RegExp', replacement: '任意字符串' }。
    //    - 工作流程: 直接将匹配 pattern 的内容替换为 replacement 字符串。这对于实现“两阶段处理”至关重要：
    //      - 示例：可以将一个复杂的 HTML 结构先替换为一个简化的、包含括号注音的纯文本格式（如 '（しごと）<em>上（じょう）</em>'）。
    //      - 这样，后续的文本处理引擎就能识别并正确地将其转换为最终的 <ruby> 注音。

    // HTML 层处理规则 (在 innerHTML 上操作，用于处理跨节点等复杂场景)
    HTML: {
      // 手动标记：自动移除 HTML 标签，简化为纯文本模式处理
      MANUAL_MARK: [
        // 支持 '?' 在任意位置，处理非标准读音
        'エアーコンディショナー（?air conditioner）',
        // 使用 '?' 作为任意 HTML 标签的占位符
        '後?（うし）',
        '的?（てき）',
        '甘み?（あまみ）',
        '辛み?（からみ）',
        '苦み?（にがみ）',
        '苦しみ?（くるしみ）',
        '深み?（ふかみ）',
        '面白み?（おもしろみ）',
        '強み?（つよみ）',
        '弱み?（よわみ）',
        '痛み?（いたみ）',
        '赤み?（あかみ）',
        '青み?（あおみ）',
        '厚み?（あつみ）',
        '憐れみ?（あわれみ）',
        '生きがい?（いきがい）',
        '教えがい?（おしえがい）',
        '育てがい?（そだてがい）',
        '作りがい?（つくりがい）',
        '状態?（じょうたい）',
        '軒並み?（のきなみ）',
        '家並み?（いえなみ）',
        '並外れた?（なみはずれた）',
        '望む?（のぞむ）',
        '期待する?（きたいする）',
        '希望する?（きぼうする）',
        '場合?（ばあい）',
        '居?（い）',
        '留守?（るす）',
        '失望?（しつぼう）',
        '非難?（ひなん）',
        '不満?（ふまん）',
        '軽蔑?（けいべつ）',
        '運動?（うんどう）',
        '戸?（と）',
        '扉?（とびら）',
        '次第?（しだい）',
        '当然?（とうぜん）',
        '安全上?（あんぜんじょう）',
        '全然?（ぜんぜん）',
        '仕事上?（しごとじょう）',
        '教育上?（きょういくじょう）',
        '歴史上?（れきしじょう）',
        '健康上?（けんこうじょう）',
        '立場上?（たちばじょう）',
        '法律上?（ほうりつじょう）',
        '経験上?（けいけんじょう）',
        '数年?（すうねん）',
        '週間?（しゅうかん）',
        '数日間?（すうじつかん）',
        '絶対?（ぜったい）',
        '何かにつけ?（なにかにつけ）',
        '泣くに泣けない?（なくになけない）',
        '言うに言えない?（いうにいえない）',
        '引くに引けない?（ひくにひけない）',
        '時?（とき）',
        '勉強中?（べんきょうちゅう）',
        '電話中?（でんわちゅう）',
      ],

      // 全替换：将复杂的 HTML 模式直接替换为指定内容，用于两阶段处理
      FULL_REPLACE: [
        // 复杂状况，手动编写 ruby
        { pattern: '一瞬（いっしゅん<br>）', replacement: '一瞬（いっしゅん）' },
        { pattern: '<b>３日</b></span><b>（</b>みっか）', replacement: '<b>３日（みっか）</b></span>' },
        { pattern: '既読スルー</b></span>（きどくするー）', replacement: '既読スルー（きどくスルー）</b></span>' },
        { pattern: '何事につけ</b></span>（なにごとにつけ）', replacement: '何事（なにごと）につけ</b></span>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（しごとじょう）', replacement: '（しごと）<em>上（じょう）</em>' },
        { pattern: '<b><span style="color: rgb(255, 0, 0);">上</span></b>（きょういくじょう）', replacement: '（きょういく）<em>上（じょう）</em>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（れきしじょう）', replacement: '（れきし）<em>上（じょう）</em>' },
        { pattern: '<b><span style="color: rgb(255, 0, 0);">上</span></b>（けんこうじょう）', replacement: '（けんこう）<em>上（じょう）</em>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（たちばじょう）', replacement: '（たちば）<em>上（じょう）</em>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（ほうりつじょう）', replacement: '（ほうりつ）<em>上（じょう）</em>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（けいけんじょう）', replacement: '（けいけん）<em>上（じょう）</em>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>上</b></span>（あんぜんじょう）', replacement: '（あんぜん）<em>上（じょう）</em>' },
        { pattern: '<span style="color: rgb(255, 0, 0);"><b>中</b></span><b><span style="color: rgb(255, 0, 0);">（きょうじゅう）に</span></b>', replacement: '（きょう）<em>中（じゅう）に</em>' },
        { pattern: '<b><span style="color: rgb(255, 0, 0);">中（あすじゅう）に</span></b>', replacement: '（あす）<em>中（じゅう）に</em>' },
        { pattern: '<b><span style="color: rgb(255, 0, 0);">中（ことしじゅう）に</span></b>', replacement: '（ことし）<em>中（じゅう）に</em>' },
        { pattern: '<b><span style="color: rgb(255, 0, 0);">中（かいしゃじゅう）に</span></b>', replacement: '（かいしゃ）<em>中（じゅう）に</em>' },
      ],
    },

    // 排除规则 用于防止脚本对特定内容进行错误的注音转换
    EXCLUDE: {
      // 全局排除：匹配完整的字符串，例如 '人称（私）'
      STRINGS: new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）', '女性（おばあちゃん）']),

      // 助词排除：当括号前是纯假名时，排除特定的助词
      PARTICLES: new Set(['は', 'が', 'を', 'に', 'で', 'と', 'から', 'まで', 'へ', 'より', 'の', 'て', 'し', 'も', 'や', 'ね', 'よ', 'さ', 'あ', 'な']),
    },
  }

  /**
   * @module PageOptimizer
   * @description 负责页面布局优化、样式注入和无关元素清理
   * 1 立即执行：在页面加载初期 document-start，立即注入 CSS 规则，隐藏所有已知的不需要元素，实现无闪烁的视觉体验
   * 2 延迟清理：在文档对象模型完全加载后 DOMContentLoaded，从结构上彻底移除所有被隐藏的元素以及页面中的脚本
   */
  const PageOptimizer = {
    _config: {
      MODULE_ENABLED: true,
      GLOBAL_REMOVE_SELECTORS: ['header#blog-header', 'footer#blog-footer', '.ldb_menu', '#analyzer_tags', '#gdpr-banner', '.adsbygoogle', '#ad_rs', '#ad2', 'div[class^="fluct-unit"]', '.article-social-btn', 'iframe[src*="clap.blogcms.jp"]', '#article-options', 'a[href*="blogmura.com"]', 'a[href*="with2.net"]', 'div[id^="ldblog_related_articles_"]'],
      STYLES: `
        #container { width: 100%; }
        @media (min-width: 960px) { #container { max-width: 960px; } }
        @media (min-width: 1040px) { #container { max-width: 1040px; } }
        #content { display: flex; position: relative; padding: 50px 0 !important; }
        #main { flex: 1; float: none !important; width: 100% !important; }
        aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }
        .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; }
        .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; }
        .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }
        .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; }
        .article-body { padding: 0 !important; }
        .article-pager { margin-bottom: 0 !important; }
        .article-body-inner { line-height: 2; opacity: 0; transition: opacity 0.3s; }
        .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; }
        .article-body-inner strike { color: orange !important; }
        .article-body-inner em { font-style: normal !important; font-weight: bold !important; color: red; }
        .to-pagetop { position: fixed; bottom: 19.2px; right: 220px; z-index: 9999; }
        rt, iframe, time, .pager, #sidebar { -webkit-user-select: none; user-select: none; }
        .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }
      `,
    },
    init() {
      if (!this._config.MODULE_ENABLED) return

      const antiFlickerCss = `${this._config.GLOBAL_REMOVE_SELECTORS.join(', ')} { display: none !important; }`
      GM_addStyle(antiFlickerCss)
      GM_addStyle(this._config.STYLES)
    },
    /**
     * 负责执行一次性的全局 DOM 清理任务
     * @private
     */
    cleanupGlobalElements() {
      if (!this._config.MODULE_ENABLED) return
      // 1. 根据配置列表，移除所有匹配的垃圾组件
      document.querySelectorAll(this._config.GLOBAL_REMOVE_SELECTORS.join(',')).forEach((el) => el.remove())
      // 2. 移除 <body> 内所有不再需要的、可能引起副作用的标签
      document.querySelectorAll('body script, body link, body style, body noscript').forEach((el) => el.remove())
    },
    /**
     * 负责单篇文章容器的收尾工作
     * @param {HTMLElement} container - 文章 `.article-body-inner` 容器元素
     */
    cleanupArticleBody(container) {
      if (!this._config.MODULE_ENABLED) return
      // 清理外层容器本身的头尾
      this._trimContainerBreaks(container)
      // 找到最后一个子元素，清理它的头尾
      const lastElement = container.lastElementChild
      if (lastElement) {
        this._trimContainerBreaks(lastElement)
      }
      container.style.opacity = 1
    },
    /**
     * 清理容器开头和结尾多余的换行和空白节点
     * @param {HTMLElement} element - 任何需要被清理头尾的 DOM 元素
     * @private
     */
    _trimContainerBreaks(element) {
      // 安全检查，如果传入的不是一个有效元素，则直接返回
      if (!element) return
      // 判断节点是否为“垃圾”
      const isJunkNode = (node) => {
        if (!node) return true
        // 检查是否为“纯空白”的文本节点
        if (node.nodeType === 3 && /^\s*$/.test(node.textContent)) {
          return true
        }
        // 检查是否为元素节点，并且是我们定义的“垃圾”标签
        if (node.nodeType === 1) {
          const tagName = node.tagName
          if (tagName === 'BR') return true
          if (tagName === 'SPAN' && /^\s*$/.test(node.textContent)) return true
          if (tagName === 'A' && /^\s*$/.test(node.textContent)) return true
        }
        return false
      }
      // 从开头移除所有垃圾节点
      while (element.firstChild && isJunkNode(element.firstChild)) {
        element.removeChild(element.firstChild)
      }
      // 从结尾移除所有垃圾节点
      while (element.lastChild && isJunkNode(element.lastChild)) {
        element.removeChild(element.lastChild)
      }
    },
    /**
     * 优化侧边栏，只保留分类并使其可见
     */
    finalizeLayout() {
      if (!this._config.MODULE_ENABLED) return
      const sidebar = document.querySelector('aside#sidebar')
      if (!sidebar) return
      const category = sidebar.querySelector('.plugin-categorize')
      // 清空侧边栏现有内容
      sidebar.innerHTML = ''
      if (category) {
        // 只将分类插件加回去
        sidebar.appendChild(category)
        // 使侧边栏可见
        sidebar.style.visibility = 'visible'
      }
    },
  }

  /**
   * @module ImageProcessor
   * @description 专门处理博客图片链接，将其转换为直接的图片元素
   * 此模块查找页面中指向 livedoor 图床的链接，并将它们替换为高质量的 `<img>` 标签，
   * 从而优化图片加载和显示体验
   */
  const ImageProcessor = {
    _config: {
      MODULE_ENABLED: true,
      // 匹配 livedoor 缩略图链接的正则表达式
      IMG_SRC_REGEX: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
    },
    /**
     * 处理指定容器内的所有图片链接
     * @param {HTMLElement} container - 包含图片链接的容器元素
     */
    process(container) {
      if (!this._config.MODULE_ENABLED) return
      container.querySelectorAll('a[href*="livedoor.blogimg.jp"]').forEach((link) => {
        const img = link.querySelector('img.pict')
        if (!img) return
        // 创建新的图片元素
        const newImg = document.createElement('img')
        newImg.loading = 'lazy'
        // 移除 '-s' 后缀以获取原图
        newImg.src = img.src.replace(this._config.IMG_SRC_REGEX, '$1$2')
        // 继承原有属性
        newImg.alt = (img.alt || '').replace(/blog/gi, '')
        Object.assign(newImg, { className: img.className, width: img.width, height: img.height })
        // 用新的图片元素替换整个链接
        link.replaceWith(newImg)
      })
    },
  }

  /**
   * @module IframeLoader
   * @description 负责 iframe 的加载策略管理
   * - lazy (懒加载) 策略: 结合浏览器原生 loading="lazy" 和 CSS 样式，实现高性能懒加载及视觉反馈
   * - click (点击加载) 策略: 将 iframe 替换为点击后才加载的占位符，实现极致的初始加载性能
   * - eager (默认) 策略: 不做任何干预，由浏览器默认处理
   */
  const IframeLoader = {
    _config: {
      MODULE_ENABLED: true,
      IFRAME_LOAD_ENABLED: true, // 可以从 setting 中获取覆盖
      IFRAME_SELECTOR: 'iframe[src*="richlink.blogsys.jp"]',
      PLACEHOLDER_CLASS: 'iframe-placeholder',
      LOADING_CLASS: 'is-loading',
      CLICKABLE_CLASS: 'is-clickable',
      STYLES: `
        @keyframes iframe-spinner-rotation { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .iframe-placeholder { position: relative; display: inline-block; vertical-align: top; background-color: #f9f9f9; box-sizing: border-box; margin: 8px 0; }
        .is-loading::after { opacity: 0.9; content: ''; position: absolute; top: 50%; left: 50%; width: 32px; height: 32px; margin-top: -16px; margin-left: -16px; border: 4px solid #ccc; border-top-color: #3B82F6; border-radius: 50%; animation: iframe-spinner-rotation 1s linear infinite; }
        .is-clickable { opacity: 0.9; display: inline-grid; place-items: center; color: #ccc; font-weight: bold; font-size: 16px; cursor: pointer; transition: background-color 0.2s, color 0.2s; -webkit-user-select: none; user-select: none; }
        .is-clickable:hover { opacity: 0.9; color: #3B82F6; background-color: #f4f8ff; }
        @media screen and (max-width: 870px) { .iframe-placeholder { max-width: 350px !important; height: 105px !important; } }
        @media screen and (min-width: 871px) { .iframe-placeholder { max-width: 580px !important; height: 120px !important; } }
      `,
    },
    init(options) {
      if (!this._config.MODULE_ENABLED) return
      Object.assign(this._config, options)
      GM_addStyle(this._config.STYLES)
    },
    replaceIframesInContainer(container) {
      if (!this._config.MODULE_ENABLED) return
      const iframes = container.querySelectorAll(this._config.IFRAME_SELECTOR)
      if (iframes.length === 0) return
      this._config.IFRAME_LOAD_ENABLED ? this._processForLazyLoad(iframes) : this._processForClickToLoad(iframes)
    },
    /**
     * 使用 IntersectionObserver 实现高性能、高可靠的懒加载
     */
    _processForLazyLoad(iframes) {
      // 1. 创建一个观察者实例，用于监视所有iframe占位符
      const observer = new IntersectionObserver(
        (entries, obs) => {
          entries.forEach((entry) => {
            // 当占位符进入或即将进入视口时
            if (entry.isIntersecting) {
              const placeholder = entry.target
              // 动态创建真正的 iframe
              const iframe = document.createElement('iframe')
              iframe.src = placeholder.dataset.src
              iframe.setAttribute('style', placeholder.dataset.style)
              iframe.setAttribute('frameborder', '0')
              iframe.setAttribute('scrolling', 'no')
              iframe.style.opacity = '0' // 初始保持透明
              iframe.addEventListener(
                'load',
                () => {
                  placeholder.classList.remove(this._config.LOADING_CLASS)
                  iframe.style.opacity = '1'
                },
                { once: true },
              )
              // 将 iframe 添加到占位符中
              placeholder.appendChild(iframe)
              // 关键优化：一旦处理完毕，就立刻停止观察此占位符，释放资源
              obs.unobserve(placeholder)
            }
          })
        },
        {
          // 预加载边距：元素距离视口底部200px时即开始加载
          rootMargin: '200px 0px',
        },
      )

      // 2. 遍历所有找到的 iframe，用占位符替换它们
      iframes.forEach((iframe) => {
        const placeholder = document.createElement('div')
        placeholder.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.LOADING_CLASS}`
        const originalStyle = iframe.getAttribute('style') || ''
        placeholder.setAttribute('style', originalStyle)
        // 将 iframe 的重要信息缓存到占位符的 data-* 属性中
        placeholder.dataset.src = iframe.src
        placeholder.dataset.style = originalStyle
        // 用占位符替换原始 iframe
        iframe.replaceWith(placeholder)
        // 让观察者开始监视这个新的占位符
        observer.observe(placeholder)
      })
    },
    _processForClickToLoad(iframes) {
      iframes.forEach((iframe) => {
        if (iframe.parentElement.classList.contains(this._config.PLACEHOLDER_CLASS)) return
        const originalSrc = iframe.src
        const originalStyle = iframe.getAttribute('style') || ''
        const placeholder = document.createElement('div')
        placeholder.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.CLICKABLE_CLASS}`
        placeholder.textContent = '▶ 関連記事を読み込む'
        placeholder.setAttribute('style', originalStyle)
        placeholder.addEventListener(
          'click',
          () => {
            const newIframe = document.createElement('iframe')
            newIframe.src = originalSrc
            newIframe.setAttribute('style', originalStyle)
            newIframe.setAttribute('frameborder', '0')
            newIframe.setAttribute('scrolling', 'no')
            const loadingWrapper = document.createElement('div')
            loadingWrapper.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.LOADING_CLASS}`
            loadingWrapper.setAttribute('style', originalStyle)
            newIframe.style.opacity = '0'
            loadingWrapper.appendChild(newIframe)
            newIframe.addEventListener(
              'load',
              () => {
                loadingWrapper.classList.remove(this._config.LOADING_CLASS)
                newIframe.style.opacity = '1'
              },
              { once: true },
            )
            placeholder.replaceWith(loadingWrapper)
          },
          { once: true },
        )
        iframe.replaceWith(placeholder)
      })
    },
  }

  /**
   * @module RubyConverter
   * @description 封装所有关于注音转换的逻辑，包括词库、规则和 DOM 处理
   * 这是脚本的核心功能模块，它遵循一个明确的优先级顺序来转换文本：
   * 优先级 0: HTML 级别强制替换 (HTML)
   * 优先级 1: 手动指定的特例词汇 (TEXT.MANUAL_MARK, TEXT.OVERRIDE_READING , TEXT.FULL_REPLACE)
   * 优先级 2: bracket 动态学习的词汇 (从【...】或「...」中提取)
   * 优先级 3: katakana片假名(英文)模式
   * 优先级 4: ruby 通用汉字(注音)模式，并应用各种排除规则
   */
  const RubyConverter = {
    // ----------------------------------------------------------------
    // § 1. 配置与核心属性 (Configuration & Core Properties)
    // ----------------------------------------------------------------
    _config: {
      MODULE_ENABLED: true,
    },
    // 原始规则配置 (将由 init 传入)
    _rawConfig: null,
    // 正则表达式集合
    _regex: {
      bracket: /[【「]([^【】「」（）・、\s～〜]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
      katakana: /([ァ-ンー]+)[（(]([\w\s+]+)[）)]/g,
      ruby: /([一-龯々]+)\s*[(（]([^（）()]*)[)）]/g,
      kanaOnly: /^[\u3040-\u309F]+$/,
      nonKana: /[^\u3040-\u309F]/,
      isKanaChar: /^[\u3040-\u309F\u30A0-\u30FF]$/,
      hasInvalidChars: /[^一-龯々\u3040-\u309F\u30A0-\u30FF]/,
    },
    // --- 以下为“词库引擎”的核心数据结构 ---
    // 已注册词条的 Set，用于高效去重
    _registeredWords: new Set(),
    // 用于构建最终 globalRegex 的词条模式源
    _wordBankForRegex: [],
    // 预处理好的 Ruby 结果缓存，我们的“最终词库”
    _rubyCache: new Map(),
    // 预编译好的 HTML 补丁集，用于修复特殊 HTML 结构
    _htmlPatches: new Map(),
    // 最终生成的“超级搜索引擎”正则
    globalRegex: null,

    // ----------------------------------------------------------------
    // § 2. 公共接口方法 (Public API Methods)
    // ----------------------------------------------------------------

    /**
     * 初始化引擎，这是外部调用的主入口。
     * 它将触发对所有静态规则的编译。
     * @param {object} rules - 原始的 RULES 对象
     */
    init(rules) {
      if (!this._config.MODULE_ENABLED) return
      this._rules = rules // 保持兼容性
      this._rawConfig = rules

      console.log('[INFO] FuranEngine: 正在编译静态规则...')
      this.compile()
      console.log(`[INFO] FuranEngine: 编译完成。已注册 ${this._registeredWords.size} 个词条，已创建 ${this._htmlPatches.size} 个 HTML 补丁。`)
    },

    /**
     * 处理一个具体的页面DOM容器。
     * 这是第二阶段“页面处理”的入口。
     * 为了保持外部调用兼容性，我们保留 applyRubyToContainer 这个名称。
     * @param {HTMLElement} container - 需要处理的DOM元素
     */
    applyRubyToContainer(container) {
      if (!this._config.MODULE_ENABLED) return
      // 步骤 2.1: 应用HTML补丁
      this._applyHtmlPatches(container)
      // 步骤 2.2: 从容器中学习新词
      this._learnFromContainer(container)
      // 步骤 2.3: 整合所有规则，生成最终的正则
      this._buildFinalRegex()
      // 步骤 2.4: 对所有文本节点应用替换链
      this._processTextNodes(container)
    },

    // ----------------------------------------------------------------
    // § 3. 内部核心方法 (Internal Core Methods)
    // ----------------------------------------------------------------

    /**
     * 阶段一：编译。
     * 此阶段处理所有与页面内容无关的静态规则。
     */
    compile() {
      this._compileStaticRules()
      this._buildFinalRegex() // 根据静态规则先构建一次
    },

    /**
     * 编译所有来自 _rawConfig 的静态规则。
     * 它会区分哪些规则可以被注册进“词库”，哪些是复杂的“HTML补丁”。
     */
    _compileStaticRules() {
      // --- HTML 规则处理 ---
      const { HTML, TEXT } = this._rawConfig

      // 1. 处理 HTML.FULL_REPLACE -> 它们总是 HTML 补丁
      HTML.FULL_REPLACE.forEach((rule) => {
        // 步骤 1: 保留原有的创建 HTML 补丁的逻辑
        const pattern = typeof rule.pattern === 'string' ? new RegExp(this._escapeRegExp(rule.pattern), 'g') : rule.pattern
        this._htmlPatches.set(pattern, rule.replacement)

        // 步骤 2: 尝试从 replacement 中提取并注册标准词条
        try {
          // 如果 replacement 字符串本身就包含 <ruby> 标签，我们假设它已经是最终形态，
          // 不再从中提取简单模式，以避免错误解析。
          if (/<ruby[^>]*>/.test(rule.replacement)) {
            return
          }

          // 使用提供的正则表达式从 replacement 中寻找所有 `汉字（读音）` 格式的组合
          const simpleRubyRegex = /([^\s\p{P}<>（）]+)（([^）]+)）/gu
          for (const match of rule.replacement.matchAll(simpleRubyRegex)) {
            const kanji = match[1]
            const reading = match[2]
            const fullPattern = `${kanji}（${reading}）`

            // 使用 registerWord 方法将提取到的词条注册到词库中
            this.registerWord({
              pattern: fullPattern,
              kanji: kanji,
              reading: reading,
              source: 'HTML.FULL_REPLACE', // 标明来源
            })
          }
        } catch (e) {
          console.error(`[ERROR] 在处理 HTML.FULL_REPLACE 规则时发生错误: "${rule.pattern}"`, e)
        }
      })

      // 2. 处理 HTML.MANUAL_MARK
      // 此部分逻辑完全采纳您的版本，以确保所有复杂情况都被正确处理为 HTML 补丁，
      // 同时补充调用 registerWord，将其纯文本版本注册到词库。
      HTML.MANUAL_MARK.forEach((patternString) => {
        // --- 分支 A：处理带 '?' 通配符的模式 ---
        if (patternString.includes('?')) {
          const [prefix, suffix] = patternString.split('?')
          if (suffix === undefined) return

          // a. 准备用于注册到词库的纯净数据
          const cleanPattern = prefix + suffix
          const readingMatchForRegister = cleanPattern.match(/（(.*?)）/)
          if (readingMatchForRegister) {
            const kanjiPartForRegister = cleanPattern.replace(/（.*?）/, '')
            const readingForRegister = readingMatchForRegister[1]

            // 【补充注册逻辑】
            // 将纯净版本注册到文本词库，以备后续在纯文本节点中使用
            this.registerWord({
              pattern: cleanPattern,
              kanji: kanjiPartForRegister,
              reading: readingForRegister,
              source: 'HTML.MANUAL_MARK',
            })
          }

          // b. 创建 HTML 补丁，用于处理 innerHTML
          const searchPattern = new RegExp(this._escapeRegExp(prefix) + '((?:<[^>]+>)*?)' + this._escapeRegExp(suffix), 'g')
          let replacementFunc
          const readingForPatch = readingMatchForRegister ? readingMatchForRegister[1] : ''

          if (!this._regex.nonKana.test(readingForPatch)) {
            const textPartForPatch = cleanPattern.replace(/（.*?）/, '')
            const rubyHtml = this._parseFurigana(textPartForPatch, readingForPatch)
            if (rubyHtml !== null) {
              replacementFunc = (match, capturedTags) => rubyHtml + (capturedTags || '')
            } else {
              replacementFunc = (match) => match
            }
          } else {
            const textOnlyForPatch = cleanPattern
            replacementFunc = (match, capturedTags) => textOnlyForPatch + (capturedTags || '')
          }
          this._htmlPatches.set(searchPattern, replacementFunc)
          // console.log(`[INFO] [补丁创建] 来源: HTML.MANUAL_MARK, 模式: "${patternString}"`)
        } else {
          // --- 分支 B：处理不带 '?' 的旧模式 ---
          const textOnly = patternString.replace(/<[^>]+>/g, '')
          const match = textOnly.match(/(.*?)（(.*?)）/)

          if (match && !this._regex.nonKana.test(match[2])) {
            const kanji = match[1]
            const reading = match[2]

            // 【补充注册逻辑】
            // 将纯净版本注册到文本词库
            this.registerWord({
              pattern: textOnly,
              kanji: kanji,
              reading: reading,
              source: 'HTML.MANUAL_MARK',
            })

            // b. 创建 HTML 补丁
            const rubyHtml = this._parseFurigana(kanji, reading)
            if (rubyHtml !== null) {
              const tags = patternString.match(/<[^>]+>/g) || []
              const closingTags = tags.join('')
              const replacement = rubyHtml + closingTags
              const pattern = new RegExp(this._escapeRegExp(patternString), 'g')
              this._htmlPatches.set(pattern, replacement)
              console.log(`[INFO] [补丁创建] 来源: HTML.MANUAL_MARK, 模式: "${patternString}"`)
            }
          } else {
            // 对于非标准注音的场景，只创建补丁，不进行注册
            const pattern = new RegExp(this._escapeRegExp(patternString), 'g')
            const tags = patternString.match(/<[^>]+>/g) || []
            const closingTags = tags.join('')
            const replacement = textOnly + closingTags
            this._htmlPatches.set(pattern, replacement)
            console.log(`[INFO] [补丁创建] 来源: HTML.MANUAL_MARK, 模式: "${patternString}" (非标准)`)
          }
        }
      })

      // --- TEXT 规则处理 ---
      // 1. 处理 TEXT.MANUAL_MARK
      TEXT.MANUAL_MARK.forEach((patternString) => {
        const match = patternString.match(/(.*?)（(.*?)）/)
        if (match) {
          this.registerWord({
            pattern: patternString,
            kanji: match[1],
            reading: match[2],
            source: 'TEXT.MANUAL_MARK',
          })
        }
      })

      // 2. 处理 TEXT.FULL_REPLACE
      TEXT.FULL_REPLACE.forEach((rule) => {
        this.registerWord({
          pattern: rule.pattern,
          replacement: rule.replacement, // 直接传递替换结果
          source: 'TEXT.FULL_REPLACE',
        })
      })
    },

    /**
     * 统一的、权威的“词条注册”方法。
     * @param {string} pattern - 格式为 `单词（读音）` 的字符串
     * @param {string} kanji - 汉字部分
     * @param {string} reading - 读音部分
     * @param {string} source - 规则来源，用于日志输出
     * @param {string} [replacement] - 可选的、直接指定的替换结果
     */
    registerWord({ pattern, kanji, reading, replacement, source }) {
      // 步骤 1: 去重检查
      if (this._registeredWords.has(pattern)) {
        return // 已注册，直接返回
      }

      // 步骤 2: 提取核心信息
      let finalReplacement = replacement

      // 步骤 3: 生成替换结果
      if (!finalReplacement) {
        if (!kanji || typeof reading === 'undefined') return
        const rubyHtml = this._parseFurigana(kanji, reading)
        if (rubyHtml === null) {
          console.warn(`[WARN] [核验失败] 来源: ${source}, 词条: "${pattern}" 将被跳过。`)
          return
        }
        finalReplacement = rubyHtml
      }
      // 步骤 4: 存入词库
      this._registeredWords.add(pattern)
      //
      this._wordBankForRegex.push(this._escapeRegExp(pattern))
      //
      this._rubyCache.set(pattern, finalReplacement)

      // 【日志修改】成功收录日志
      const logType = replacement ? ' (直接替换)' : ''
      console.log(`[INFO] [成功收录] 来源: ${source}, 词条: "${pattern}"${logType}`)
    },

    /**
     * 从容器中学习新词，并注册它们。
     * @param {HTMLElement} container
     */
    _learnFromContainer(container) {
      const html = container.innerHTML
      for (const match of html.matchAll(this._regex.bracket)) {
        const kanjiPart = match[1]
        const readingPart = match[2]
        const corePattern = `${kanjiPart}（${readingPart}）`

        // 进行一些基本检查
        if (this._regex.nonKana.test(readingPart) || this._regex.hasInvalidChars.test(kanjiPart)) {
          continue
        }

        this.registerWord({
          pattern: corePattern,
          kanji: kanjiPart,
          reading: readingPart,
          source: '动态学习', // 来源指定
        })
      }
    },

    /**
     * 应用 HTML 补丁来修复特殊结构。
     * @param {HTMLElement} container
     */
    _applyHtmlPatches(container) {
      if (this._htmlPatches.size === 0) return
      let html = container.innerHTML
      const originalHtml = html
      this._htmlPatches.forEach((replacement, pattern) => {
        html = html.replace(pattern, replacement)
      })
      if (html !== originalHtml) {
        container.innerHTML = html
      }
    },

    /**
     * 根据当前的词库，构建或重建 globalRegex。
     */
    _buildFinalRegex() {
      this.globalRegex = this._wordBankForRegex.length > 0 ? new RegExp(`(${this._wordBankForRegex.join('|')})`, 'g') : null
    },

    /**
     * 遍历容器的所有文本节点，并应用替换链。
     * @param {HTMLElement} root
     */
    _processTextNodes(root) {
      const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
        acceptNode: (n) => (n.parentNode.nodeName !== 'SCRIPT' && n.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
      })
      const nodesToProcess = []
      let node
      while ((node = walker.nextNode())) {
        const newContent = this._applyTextReplacements(node.nodeValue)
        if (newContent !== node.nodeValue) {
          nodesToProcess.push({ node, newContent })
        }
      }
      // 从后往前替换，避免影响 DOM 结构
      for (let i = nodesToProcess.length - 1; i >= 0; i--) {
        const { node, newContent } = nodesToProcess[i]
        const fragment = document.createRange().createContextualFragment(newContent)
        node.parentNode.replaceChild(fragment, node)
      }
    },

    /**
     * 对纯文本内容执行三步替换链。
     * @param {string} text
     * @returns {string}
     */
    _applyTextReplacements(text) {
      if (!text.includes('（') && !text.includes('(')) {
        return text
      }

      let processedText = text

      // 替换链 1: 高优先级词库匹配
      if (this.globalRegex) {
        // console.log('[INFO] RubyCache:', this._rubyCache)
        processedText = processedText.replace(this.globalRegex, (match) => {
          return this._rubyCache.get(match) || match
        })
      }

      // 替换链 2: 片假名模式即时匹配
      processedText = processedText.replace(this._regex.katakana, (_, katakana, romaji) => `<ruby>${katakana}<rt>${romaji}</rt></ruby>`)

      // 替换链 3: 通用汉字模式后备匹配 (含排除检查)
      processedText = processedText.replace(this._regex.ruby, (match, kanji, reading) => {
        const fullMatch = `${kanji}（${reading}）`
        if (this._rawConfig.EXCLUDE.STRINGS.has(fullMatch) || this._regex.nonKana.test(reading)) {
          return match // 满足排除条件，不转换
        }
        return `<ruby>${kanji}<rt>${reading}</rt></ruby>`
      })

      return processedText
    },

    // ----------------------------------------------------------------
    // § 4. 工具/辅助方法 (Utility/Helper Methods)
    // ----------------------------------------------------------------

    _parseFurigana(kanji, reading) {
      const hiraganaReading = this._katakanaToHiragana(reading)
      let result = ''
      let kanjiIndex = 0
      let readingIndex = 0
      while (kanjiIndex < kanji.length) {
        const currentKanjiChar = kanji[kanjiIndex]
        if (this._regex.isKanaChar.test(currentKanjiChar)) {
          result += currentKanjiChar
          const hiraganaCurrent = this._katakanaToHiragana(currentKanjiChar)
          const tempNextReadingIndex = hiraganaReading.indexOf(hiraganaCurrent, readingIndex)
          if (tempNextReadingIndex !== -1) {
            readingIndex = tempNextReadingIndex + hiraganaCurrent.length
          } else {
            return null // 严重错误：假名在读音中不匹配
          }
          kanjiIndex++
        } else {
          let kanjiPart = ''
          let blockEndIndex = kanjiIndex
          while (blockEndIndex < kanji.length && !this._regex.isKanaChar.test(kanji[blockEndIndex])) {
            kanjiPart += kanji[blockEndIndex]
            blockEndIndex++
          }
          const nextKanaInKanji = kanji[blockEndIndex]
          let readingEndIndex
          if (nextKanaInKanji) {
            const hiraganaNextKana = this._katakanaToHiragana(nextKanaInKanji)
            readingEndIndex = hiraganaReading.indexOf(hiraganaNextKana, readingIndex)
            if (readingEndIndex === -1) {
              readingEndIndex = hiraganaReading.length
            }
          } else {
            readingEndIndex = hiraganaReading.length
          }
          const readingPart = reading.substring(readingIndex, readingEndIndex)
          if (kanjiPart) {
            if (!readingPart) {
              return null // 严重错误：汉字部分没有对应的读音
            } else {
              result += `<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`
            }
          }
          readingIndex = readingEndIndex
          kanjiIndex = blockEndIndex
        }
      }
      // 最后检查是否有多余的读音未被使用
      if (readingIndex < hiraganaReading.length) {
        // 在某些情况下，末尾的送假名可能在kanji部分省略，这里可以根据需要放宽或收紧
        // 为保持严谨，我们暂时也视为一种潜在问题
        console.warn(`[WARN] 发现 "${kanji}" 存在多余的读音字符`)
      }
      return result
    },

    _katakanaToHiragana(str) {
      if (!str) return ''
      return str.replace(/[\u30A1-\u30F6]/g, (match) => String.fromCharCode(match.charCodeAt(0) - 0x60))
    },

    _escapeRegExp: (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
  }

  /**
   * @module SettingsPanel
   * @description 管理设置面板的创建、事件处理和状态持久化
   * 此模块负责在页面上创建一个浮动设置面板，允许用户动态地开关脚本功能和调整振假名 (Furigana) 的可见性
   * 所有设置都会通过 GM_setValue/GM_getValue 进行持久化保存
   */
  const SettingsPanel = {
    _config: {
      MODULE_ENABLED: true, // 是否启用设置面板
      FEEDBACK_URL: 'https://greasyfork.org/scripts/542386-edewakaru-enhanced',
      OPTIONS: {
        SCRIPT_ENABLED: { label: 'ページ最適化', defaultValue: true, handler: '_handleScriptToggle', isChild: false },
        FURIGANA_VISIBLE: { label: '振り仮名表示', defaultValue: true, handler: '_handleFuriganaToggle', isChild: true },
        IFRAME_LOAD_ENABLED: { label: '関連記事表示', defaultValue: true, handler: '_handleIframeLoadToggle', isChild: true },
        TTS_ENABLED: { label: '単語選択発音', defaultValue: false, handler: '_handleTtsToggle', isChild: true },
      },
      STYLES: `
        #settings-panel { position: fixed; bottom: 24px; right: 24px; z-index: 9999; display: flex; flex-direction: column; gap: 8px; padding: 16px; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.9; -webkit-user-select: none; user-select: none; }
        .settings-title { font-size: 14px; font-weight: 600; color: #1F2937; margin: 0 0 6px 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 6px; position: relative; }
        .feedback-link, .feedback-link:visited { position: absolute; top: 0; right: 0; width: 16px; height: 16px; color: #E5E7EB !important; transition: color 0.2s ease-in-out; }
        .feedback-link:hover { color: #3B82F6 !important; }
        .feedback-link svg { width: 100%; height: 100%; }
        .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 8px; }
        .setting-label { font-size: 13px; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
        .toggle-switch { position: relative; display: inline-block; width: 40px; height: 20px; flex-shrink: 0; }
        .toggle-switch.disabled { opacity: 0.5; pointer-events: none; }
        .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; }
        .toggle-slider:before { position: absolute; content: ""; height: 15px; width: 15px; left: 2.5px; bottom: 2.5px; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
        input:checked+.toggle-slider { background-color: #3B82F6; }
        input:checked+.toggle-slider:before { transform: translateX(20px); }
        .settings-notification { position: fixed; bottom: 208px; right: 24px; z-index: 9999; padding: 8px 12px; background-color: #3B82F6; color: white; border-radius: 6px; font-size: 13px; animation: slideInOut 3s ease-in-out; -webkit-user-select: none; user-select: none; }
        @keyframes slideInOut { 0%, 100% { opacity: 0; transform: translateX(20px); } 15%, 85% { opacity: 0.9; transform: translateX(0); } }
      `,
    },
    // 用于缓存 UI 元素，避免重复查询 DOM
    _uiElements: {},
    init() {
      GM_addStyle(this._config.STYLES)
      this._createPanel()
      this._initializeFuriganaDisplay()
    },
    /**
     * 从存储中获取所有设置的原始值。
     * @returns {object} - 一个包含所有选项当前值的对象，键名保持全大写。
     */
    getOptions() {
      const options = {}
      for (const key in this._config.OPTIONS) {
        options[key] = GM_getValue(key, this._config.OPTIONS[key].defaultValue)
      }
      return options
    },
    // 事件处理器：处理主开关切换
    _handleScriptToggle(enabled) {
      GM_setValue('SCRIPT_ENABLED', enabled)
      this._showNotification()
      this._updateChildOptionsUI(enabled)
    },
    // 事件处理器：处理振假名可见性切换
    _handleFuriganaToggle(visible) {
      GM_setValue('FURIGANA_VISIBLE', visible)
      this._toggleFuriganaDisplay(visible)
    },
    // 事件处理器：处理 iframe 加载切换
    _handleIframeLoadToggle(enabled) {
      GM_setValue('IFRAME_LOAD_ENABLED', enabled)
      this._showNotification()
    },
    // 事件处理器：处理 TTS 开关切换
    _handleTtsToggle(enabled) {
      GM_setValue('TTS_ENABLED', enabled)
      // TODO：耦合需要优化
      enabled ? ContextMenu.init() : ContextMenu.destroy()
    },
    // 切换振假名显示状态
    _toggleFuriganaDisplay(visible) {
      const id = 'furigana-display-style'
      let style = document.getElementById(id)
      if (!style) {
        style = document.createElement('style')
        style.id = id
        document.head.appendChild(style)
      }
      style.textContent = `rt { display: ${visible ? 'ruby-text' : 'none'} !important; }`
    },
    // 根据保存的设置初始化振假名显示状态
    _initializeFuriganaDisplay() {
      // 因为注音默认就是可见的，不需要处理
      if (!GM_getValue('FURIGANA_VISIBLE', this._config.OPTIONS.FURIGANA_VISIBLE.defaultValue)) {
        this._toggleFuriganaDisplay(false)
      }
    },
    _createPanel() {
      if (!this._config.MODULE_ENABLED) return
      const panel = document.createElement('div')
      panel.id = 'settings-panel'
      panel.innerHTML = `
        <h3 class="settings-title">
          設定パネル
          <a href="${this._config.FEEDBACK_URL}" target="_blank" rel="noopener noreferrer" class="feedback-link" title="Feedback">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
          </a>
        </h3>
      `
      const isMasterEnabled = GM_getValue('SCRIPT_ENABLED', this._config.OPTIONS.SCRIPT_ENABLED.defaultValue)
      // 直接遍历 _config.OPTIONS 来创建所有 UI 开关
      for (const key in this._config.OPTIONS) {
        const config = this._config.OPTIONS[key]
        let isDisabled = config.isChild && !isMasterEnabled
        if (key === 'TTS_ENABLED' && !('speechSynthesis' in window)) {
          isDisabled = true
        }
        panel.appendChild(this._createToggle(key, config, isDisabled))
      }
      document.body.appendChild(panel)
    },
    _createToggle(key, config, isDisabled) {
      const { label, handler, defaultValue } = config
      const id = `setting-${key.toLowerCase()}`
      const itemContainer = document.createElement('div')
      itemContainer.className = 'setting-item'
      itemContainer.dataset.key = key

      const isChecked = GM_getValue(key, defaultValue)
      itemContainer.innerHTML = `
        <label for="${id}" class="setting-label">${label}</label>
        <label class="toggle-switch ${isDisabled ? 'disabled' : ''}">
          <input type="checkbox" id="${id}" ${isChecked ? 'checked' : ''}>
          <span class="toggle-slider"></span>
        </label>
      `
      const toggleSwitch = itemContainer.querySelector('.toggle-switch')
      this._uiElements[key] = { switch: toggleSwitch }
      itemContainer.querySelector('input').addEventListener('change', (e) => this[handler](e.target.checked))
      return itemContainer
    },
    _updateChildOptionsUI(masterEnabled) {
      for (const key in this._config.OPTIONS) {
        if (this._config.OPTIONS[key].isChild) {
          const uiElement = this._uiElements[key]
          if (uiElement && uiElement.switch) {
            uiElement.switch.classList.toggle('disabled', !masterEnabled)
          }
        }
      }
    },
    // 显示通知
    _showNotification(message = '設定を保存しました。再読み込みしてください。') {
      const el = document.createElement('div')
      el.className = 'settings-notification'
      el.textContent = message
      document.body.appendChild(el)
      setTimeout(() => el.remove(), 3000)
    },
  }

  /**
   * @module TTSPlayer
   * @description
   */
  const TTSPlayer = {
    _config: {
      VOICE_NAMES: ['Microsoft Nanami Online (Natural) - Japanese (Japan)', 'Microsoft Keita Online (Natural) - Japanese (Japan)'],
      LANG: 'ja-JP',
    },

    _initPromise: null,
    _voices: [],

    async speak(text) {
      if (!this._initPromise) {
        this._initPromise = this._initialize()
      }
      await this._initPromise
      speechSynthesis.cancel()
      if (!text?.trim() || this._voices.length === 0) {
        this._voices.length === 0 && console.warn('[WARN] [TTSPlayer] TTS 功能不可用，无法播放。')
        return
      }
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.voice = this._voices[Math.floor(Math.random() * this._voices.length)]
      utterance.lang = this._config.LANG
      utterance.onerror = (e) => {
        !['canceled', 'interrupted'].includes(e.error) && console.error(`[ERROR] [TTSPlayer] TTS 播放错误: ${e.error}`)
      }
      speechSynthesis.speak(utterance)
    },

    _initialize() {
      return new Promise((resolve) => {
        if (!('speechSynthesis' in window)) {
          console.warn('[WARN] [TTSPlayer] 浏览器不支持语音合成 (SpeechSynthesis)。')
          return resolve()
        }
        console.log('[INFO] [TTSPlayer] 正在初始化并加载语音列表...')
        let resolved = false

        const loadVoices = () => {
          if (resolved) return
          resolved = true
          const allVoices = speechSynthesis.getVoices()
          const { VOICE_NAMES, LANG } = this._config
          this._voices = allVoices.filter((v) => VOICE_NAMES.includes(v.name) && v.lang === LANG)
          if (this._voices.length > 0) {
            console.log(`[INFO] [TTSPlayer] 初始化成功，找到 ${this._voices.length} 个可用日语音色。`)
          } else {
            console.warn('[WARN] [TTSPlayer] 未找到指定的日语音色，TTS 功能可能不可用。')
          }
          // 清理事件监听器
          speechSynthesis.onvoiceschanged = null
          resolve()
        }

        // 三重保险机制
        const initialVoices = speechSynthesis.getVoices()
        if (initialVoices.length > 0) {
          loadVoices()
        } else {
          speechSynthesis.onvoiceschanged = loadVoices
          setTimeout(loadVoices, 500)
        }
      })
    },
  }

  /**
   * @module ContextMenu
   * @description 管理划词出现的 TTS 快捷菜单
   */
  const ContextMenu = {
    _config: {
      MODULE_ENABLED: true,
      MENU_ID: 'selection-context-menu',
      MENU_OFFSET: 8, // 图标相对于鼠标指针的偏移量
      VALID_SELECTION_AREA: '.article-body-inner', // 可配置的有效选择区域
      MIN_SELECTION_LENGTH: 1, // 最小选择长度
      DRAG_THRESHOLD: 5, // 拖拽阈值，避免微小移动触发拖拽
      EMOJI_REGEX: /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu,
      STYLES: `
        #selection-context-menu { position: absolute; top: 0; left: 0; display: none; z-index: 9999; opacity: 0; user-select: none; will-change: transform, opacity; pointer-events: none; }
        #selection-context-menu.visible { opacity: 0.9; pointer-events: auto; transition: opacity 0.1s ease-out, transform 0.1s ease-out; }
        #selection-context-menu button { display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0; border-radius: 50%; cursor: pointer; border: none; background-color: #3B82F6; color: #FFFFFF; box-shadow: 0 5px 15px rgba(0,0,0,0.15), 0 2px 5px rgba(0,0,0,0.1); transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out; outline: none; }
        #selection-context-menu button:hover { background-color: #4B90F8; transform: scale(1.05); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3); }
        #selection-context-menu button.is-dragging { cursor: grabbing; transform: scale(1); opacity: 0.7; transition: none; }
        #selection-context-menu button svg { width: 20px; height: 20px; stroke: currentColor; stroke-width: 2; pointer-events: none; }
      `,
    },
    menuElement: null,
    isDragging: false,
    dragUpdatePending: false,
    position: { x: 0, y: 0 },
    dragOffset: { x: 0, y: 0 },
    dragStart: { x: 0, y: 0 },
    abortController: null,
    boundHandlers: {
      dragMove: null,
      dragEnd: null,
    },

    init(options) {
      if (!this._config.MODULE_ENABLED) return
      if (this.menuElement) return
      Object.assign(this._config, options)
      GM_addStyle(this._config.STYLES)
      this._createMenu()
      this._bindEvents()
    },

    destroy() {
      if (!this.menuElement) return
      // 1. 清理由 AbortController 管理的静态事件
      this.abortController?.abort()
      // 2. 确保清理动态添加的拖拽事件
      if (this.boundHandlers.dragMove) document.removeEventListener('mousemove', this.boundHandlers.dragMove)
      if (this.boundHandlers.dragEnd) document.removeEventListener('mouseup', this.boundHandlers.dragEnd)
      // 3. 移除 DOM 元素
      this.menuElement.remove()
      // 4. 重置所有状态
      this._resetState()
    },

    _resetState() {
      this.menuElement = null
      this.isDragging = false
      this.dragUpdatePending = false
      this.position = { x: 0, y: 0 }
      this.dragOffset = { x: 0, y: 0 }
      this.dragStart = { x: 0, y: 0 }
      this.abortController = null
      this.boundHandlers = {
        dragMove: null,
        dragEnd: null,
      }
    },

    _bindEvents() {
      this.abortController = new AbortController()
      const { signal } = this.abortController
      // 这些是“静态”事件，在组件生命周期内一直存在
      this.menuElement.addEventListener('mousedown', this._handleDragStart.bind(this), { signal })
      document.addEventListener('mousedown', this._handleMouseDown.bind(this), { signal })
      document.addEventListener('mouseup', this._handleMouseUp.bind(this), { signal })
      document.addEventListener('keydown', this._handleKeyDown.bind(this), { signal })
      // 为拖拽事件预先绑定 this，以便能正确移除
      this.boundHandlers.dragMove = this._handleDragMove.bind(this)
      this.boundHandlers.dragEnd = this._handleDragEnd.bind(this)
    },

    _createMenu() {
      if (document.getElementById(this._config.MENU_ID)) return
      this.menuElement = document.createElement('div')
      this.menuElement.id = this._config.MENU_ID
      const readButton = document.createElement('button')
      readButton.title = 'Read'
      readButton.setAttribute('aria-label', 'Read selected text')
      readButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>`
      readButton.addEventListener('click', (event) => {
        if (this.isDragging) {
          event.stopPropagation()
          return
        }
        const cleanedText = this._getCleanedSelectedText()
        if (cleanedText) {
          // TODO：耦合需要优化
          TTSPlayer.speak(cleanedText)
        }
      })
      this.menuElement.appendChild(readButton)
      document.body.appendChild(this.menuElement)
    },

    _getCleanedSelectedText() {
      try {
        const selection = window.getSelection()
        if (!selection || selection.rangeCount === 0) return ''
        const range = selection.getRangeAt(0)
        const fragment = range.cloneContents()
        const tempDiv = document.createElement('div')
        tempDiv.appendChild(fragment)
        // 清理操作
        const cleanupTasks = [
          () => tempDiv.querySelectorAll('rt').forEach((el) => el.remove()), // 移除注音
          () => tempDiv.querySelectorAll('br').forEach((el) => el.replaceWith('。')), // 替换换行
        ]
        cleanupTasks.forEach((task) => task())
        // 获取并清理文本
        let text = tempDiv.textContent || ''
        text = text.replace(this._config.EMOJI_REGEX, '。') // 替换 Emoji
        text = text.replace(/\s+/g, '') // 清理空白

        return text
      } catch (error) {
        console.error('[ERROR] [ContextMenu] 清理选中文本时出错:', error)
        return ''
      }
    },

    _handleDragStart(event) {
      event.preventDefault()
      event.stopPropagation()
      this.isDragging = false
      this.dragStart = { x: event.pageX, y: event.pageY }
      this.dragOffset = { x: event.pageX - this.position.x, y: event.pageY - this.position.y }
      this.menuElement.style.transition = 'none' // 拖拽时禁用动画
      // 使用预先绑定的引用来添加监听器
      document.addEventListener('mousemove', this.boundHandlers.dragMove)
      document.addEventListener('mouseup', this.boundHandlers.dragEnd, { once: true })
    },

    _handleDragMove(event) {
      event.preventDefault()
      if (this.dragUpdatePending) return
      this.dragUpdatePending = true

      // 仅当未开始拖拽时，才检查阈值
      if (!this.isDragging) {
        const dx = event.pageX - this.dragStart.x
        const dy = event.pageY - this.dragStart.y
        if (Math.sqrt(dx * dx + dy * dy) > this._config.DRAG_THRESHOLD) {
          this.isDragging = true
          this.menuElement.querySelector('button')?.classList.add('is-dragging')
        }
      }

      requestAnimationFrame(() => {
        // 仅当拖拽状态为真时，才更新位置
        if (this.isDragging) {
          this.position.x = event.pageX - this.dragOffset.x
          this.position.y = event.pageY - this.dragOffset.y
          this.menuElement.style.transform = `translate(${this.position.x}px, ${this.position.y}px)`
        }
        this.dragUpdatePending = false
      })
    },

    _handleDragEnd() {
      // 使用相同的引用来移除监听器
      document.removeEventListener('mousemove', this.boundHandlers.dragMove)
      this.menuElement.querySelector('button')?.classList.remove('is-dragging')
      this.menuElement.style.transition = '' // 恢复动画
      setTimeout(() => {
        this.isDragging = false
      }, 0)
    },

    _handleMouseDown(event) {
      // 如果 mousedown 事件发生在菜单本身，则让拖拽处理器接管，不执行任何操作
      if (this.menuElement.contains(event.target)) {
        return
      }
      // 对于页面上任何其他地方的 mousedown，都意味着一次新交互的开始。
      // 立即隐藏菜单，以防止在开始新的文本选择时，旧的图标仍然残留。
      this._hideMenu()
    },

    _handleMouseUp(event) {
      // 忽略拖拽或点击菜单自身的事件
      if (this.isDragging || this.menuElement.contains(event.target)) {
        return
      }

      // 使用 setTimeout 推迟执行，以确保浏览器有足够时间完成选区状态的更新。
      // 这是必要的，因为 mouseup 事件触发时，selection 对象可能尚未最终确定。
      setTimeout(() => {
        const selection = window.getSelection()
        const selectedText = selection.toString().trim()
        const clickTargetIsValid = event.target.closest(this._config.VALID_SELECTION_AREA)

        // 核心决策：当且仅当用户在有效区域内完成了一次有效的文本选择时，才显示菜单。
        if (clickTargetIsValid && selectedText.length >= this._config.MIN_SELECTION_LENGTH) {
          this._showMenu(event.pageX, event.pageY)
        } else {
          // 在所有其他情况下，隐藏菜单并清除选区，确保行为一致。
          this._hideMenu()
          selection?.removeAllRanges()
        }
      }, 0)
    },

    _handleKeyDown(event) {
      if (event.key === 'Escape' && this.menuElement && this.menuElement.classList.contains('visible')) {
        this._hideMenu()
        window.getSelection()?.removeAllRanges()
      }
    },

    _showMenu(x, y) {
      if (!this.menuElement) return
      this.position.x = x + this._config.MENU_OFFSET
      this.position.y = y + this._config.MENU_OFFSET
      this.menuElement.style.transition = 'none'
      this.menuElement.style.transform = `translate(${this.position.x}px, ${this.position.y}px)`
      this.menuElement.style.display = 'block'
      requestAnimationFrame(() => {
        this.menuElement.style.transition = ''
        this.menuElement.classList.add('visible')
      })
    },

    _hideMenu() {
      if (!this.menuElement || !this.menuElement.classList.contains('visible')) return
      // 移除 .visible 类，由于 transition 只在 .visible 上定义，所以隐藏是瞬时的
      this.menuElement.classList.remove('visible')
      // 立即设置 display: none，因为隐藏是瞬时的，不需要等待 transitionend
      this.menuElement.style.display = 'none'
    },
  }
  /**
   * @module MainController
   * @description 脚本主控制器，负责协调所有模块的初始化和执行流程
   */
  const MainController = {
    run() {
      // 从 SettingsPanel 获取所有持久化的用户设置
      const options = SettingsPanel.getOptions()

      // 步骤 1: 检查脚本是否被用户禁用如果禁用，则仅加载设置面板，不执行任何页面修改
      if (!options.SCRIPT_ENABLED) {
        document.addEventListener('DOMContentLoaded', () => SettingsPanel.init())
        return
      }

      // 步骤 2: 在 document-start 阶段，立即执行不依赖 DOM 内容的操作，以尽快生效
      PageOptimizer.init() // 注入样式，防止页面闪烁
      RubyConverter.init(RULES) // 预处理词库，为后续操作做准备

      // 步骤 3: 等待 DOM 完全加载后，执行依赖 DOM 内容的操作
      document.addEventListener('DOMContentLoaded', () => {
        PageOptimizer.cleanupGlobalElements()
        IframeLoader.init(options)
        SettingsPanel.init()
        if (options.TTS_ENABLED) ContextMenu.init()
        this._processPageContent()
      })
    },

    /**
     * 编排所有对页面主要内容的处理流程
     */
    _processPageContent() {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length === 0) {
        // 即使没有文章，也需要执行最终布局，以显示侧边栏
        PageOptimizer.finalizeLayout()
        return
      }

      let currentIndex = 0
      /**
       * 编排所有对页面主要内容的处理流程
       * 此方法采用异步分批处理（Asynchronous Batch Processing）的策略
       * 以避免在处理包含大量文章的长页面时，因脚本长时间占用主线程而导致的页面卡顿或无响应
       */
      const processBatch = () => {
        // 定义批次大小，每次处理 2 篇文章
        // Math.min 确保最后一批不会超出数组范围
        const batchSize = Math.min(2, articleBodies.length - currentIndex)
        const endIndex = currentIndex + batchSize

        // 在当前帧内，同步处理本批次的所有文章
        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i]
          // 1. 最先处理，确保在最原始的 DOM 上工作
          RubyConverter.applyRubyToContainer(body)
          // 2. 替换iframe为占位符
          IframeLoader.replaceIframesInContainer(body)
          // 3. 替换图片链接
          ImageProcessor.process(body)
          // 4. 最后做收尾清理和显示
          PageOptimizer.cleanupArticleBody(body)
        }
        // 将索引移动到下一批次的起始位置
        currentIndex = endIndex
        // 检查是否还有未处理的文章
        if (currentIndex < articleBodies.length) {
          // 使用 requestAnimationFrame 请求浏览器在下一次重绘前调用 processBatch
          requestAnimationFrame(processBatch)
        } else {
          // 所有批次处理完成，执行最终的全局布局调整
          PageOptimizer.finalizeLayout()
        }
      }
      // 启动第一个批次的处理
      requestAnimationFrame(processBatch)
    },
  }
  MainController.run()
})()
