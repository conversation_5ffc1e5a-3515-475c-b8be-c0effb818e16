const ContextMenu = {
  _config: {
    MODULE_ENABLED: true,
    MENU_ID: 'selection-context-menu',
    STYLES: `
      #selection-context-menu { position: absolute; display: none; z-index: 9999; opacity: 0; transition: opacity 0.1s ease-out; user-select: none; }
      #selection-context-menu.visible { opacity: 0.9; }
      #selection-context-menu button { display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0; border-radius: 50%; cursor: grab; border: none; background-color: #3B82F6; color: #FFFFFF; box-shadow: 0 5px 15px rgba(0,0,0,0.15), 0 2px 5px rgba(0,0,0,0.1); transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out; }

      #selection-context-menu button:active { cursor: grabbing; }
      #selection-context-menu button svg { width: 20px; height: 20px; stroke: currentColor; stroke-width: 2; pointer-events: none; }
    `,
  },
  menuElement: null,
  isDragging: false,
  dragOffsetX: 0,
  dragOffsetY: 0,
  boundDragMove: null,
  boundDragEnd: null,

  init(options) {
    if (!this._config.MODULE_ENABLED) return
    Object.assign(this._config, options)
    GM_addStyle(this._config.STYLES)
    this._createMenu()
    this._bindEvents()
  },
  _createMenu() {
    if (document.getElementById(this._config.MENU_ID)) return
    this.menuElement = document.createElement('div')
    this.menuElement.id = this._config.MENU_ID
    const readButton = document.createElement('button')
    readButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>`
    readButton.addEventListener('click', (event) => {
      if (this.isDragging) {
        event.stopPropagation()
        return
      }
      const cleanedText = this._getCleanedSelectedText()
      if (cleanedText) {
        TTSPlayer.speak(cleanedText)
      }
    })
    this.menuElement.appendChild(readButton)
    document.body.appendChild(this.menuElement)
  },
  _getCleanedSelectedText() {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return ''
    const tempContainer = document.createElement('div')
    tempContainer.appendChild(selection.getRangeAt(0).cloneContents())
    tempContainer.querySelectorAll('rt').forEach((el) => el.remove())
    tempContainer.querySelectorAll('br').forEach((el) => el.replaceWith('。'))
    let text = tempContainer.textContent
    const emojiRegex = /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu
    text = text.replace(emojiRegex, '。')
    text = text.replace(/\s+/g, '')
    return text
  },
  _bindEvents() {
    this.menuElement.addEventListener('mousedown', this._handleDragStart.bind(this))
    document.addEventListener('mouseup', this._handleMouseUp.bind(this))
  },
  _handleDragStart(event) {
    event.preventDefault()
    event.stopPropagation()
    this.isDragging = false
    const rect = this.menuElement.getBoundingClientRect()
    this.dragOffsetX = event.clientX - rect.left
    this.dragOffsetY = event.clientY - rect.top
    this.boundDragMove = this._handleDragMove.bind(this)
    this.boundDragEnd = this._handleDragEnd.bind(this)
    document.addEventListener('mousemove', this.boundDragMove)
    document.addEventListener('mouseup', this.boundDragEnd)
  },
  _handleDragMove(event) {
    event.preventDefault()
    this.isDragging = true
    this.menuElement.style.transition = 'none'
    this.menuElement.style.left = `${event.pageX - this.dragOffsetX}px`
    this.menuElement.style.top = `${event.pageY - this.dragOffsetY}px`
  },
  _handleDragEnd() {
    document.removeEventListener('mousemove', this.boundDragMove)
    document.removeEventListener('mouseup', this.boundDragEnd)
    this.menuElement.style.transition = ''
    setTimeout(() => {
      this.isDragging = false
    }, 0)
  },
  _handleMouseUp(event) {
    if (this.isDragging || (this.menuElement && this.menuElement.contains(event.target))) {
      return
    }
    setTimeout(() => {
      const selectedText = window.getSelection().toString().trim()
      if (selectedText.length > 0) {
        this._showMenu(event.pageX, event.pageY)
      } else {
        this._hideMenu()
      }
    }, 10)
  },
  _showMenu(x, y) {
    if (!this.menuElement) return
    this.menuElement.style.left = `${x + 8}px`
    this.menuElement.style.top = `${y + 8}px`
    requestAnimationFrame(() => {
      this.menuElement.style.display = 'block'
      this.menuElement.classList.add('visible')
    })
  },
  _hideMenu() {
    if (!this.menuElement || !this.menuElement.classList.contains('visible')) return
    this.menuElement.classList.remove('visible')
  },
}
