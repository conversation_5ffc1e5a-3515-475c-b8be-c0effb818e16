# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Userscript** project called "Edewakaru Enhanced" that enhances the reading experience on the Japanese language learning website "絵でわかる日本語" (https://www.edewakaru.com/). 

Core functionalities:
- Auto-convert kanji pronunciations from parentheses to ruby annotations
- Hide advertisements and unnecessary page elements
- Optimize page layout for reading
- Add text-to-speech support for Japanese text
- Provide a customizable settings panel

## Development Commands

```bash
# Basic development
pnpm run dev          # Start Vite development server
pnpm run build        # Build userscript for production
pnpm run preview      # Preview built userscript

# Code quality
pnpm run lint:fix     # Auto-fix linting issues
pnpm run test         # Run all tests once
pnpm run test:watch   # Run tests in watch mode

# Build & Release
pnpm run compress     # Compress single file
pnpm run postbuild    # Post-build compression
pnpm run update-version # Update version and publish
pnpm run publish      # Full release process
```

## Architecture

### Entry Points
- `src/main.js:15` - Initializes MainController
- `src/modules/MainController.js` - Central orchestrator class

### Core Components
- `MainController` - Initializes all modules and coordinates functionality
- `ContextMenu` - Right-click context menu for TTS and settings
- `IframeLoader` - Handles iframe-related content loading
- `ImageProcessor` - Processes and optimizes images (lazy loading, quality)
- `PageOptimizer` - Removes ads and optimizes layout
- `RubyConverter` - Converts kanji pronunciation format to ruby annotations
- `SettingsPanel` - Creates interactive settings UI
- `TTSPlayer` - Text-to-speech functionality

### Utility Components
- `EventBus` - Centralized event communication system
- `config/rules.js` - Static rules for furigana conversion

### Testing Structure
- `tests/RubyConverter/` - Comprehensive tests for RubyConverter module
- `tests/RubyConverter.test.js` - Main RubyConverter test file

### Build System
- **Monorepo structure** using Vite with vite-plugin-monkey
- **Userscript metadata** automatically generated based on vite.config.js
- **Minification** disabled for GreasyFork compatibility
- **Target sites**: www.edewakaru.com and edewakaru-doshi.blog.jp
- **Build output**: `dist/edewakaru.user.js`

## Key Patterns

### Module System
- Modular ES6 classes in `src/modules/`
- Event-driven architecture via EventBus
- Lazy initialization pattern in MainController
- Settings-driven feature activation

### Settings Control
- Settings stored in GM storage
- Real-time toggle controls in bottom-right settings panel
- Three-state system: immediate effect, refresh required, or page reload required

### Data Flow
1. Page load triggers MainController initialization
2. Settings loaded from GM storage
3. RubyConverter processes text content based on static rules
4. PageOptimizer modifies DOM structure
5. TTS and image processing available as interactive features

## Language Support

- **Primary**: Japanese (edewakaru.com)
- **UI Language**: English/Japanese/Chinese (translated settings)
- **TTS**: Japanese voices (Edge browser only)

## File Structure Essentials

```
src/
├── main.js                 # Entry point
├── edewakaru.js            # Older userscript version
├── config/rules.js         # Furigana conversion rules
├── modules/                # Core functionality
├── utils/                  # Shared utilities
dist/                       # Build output (.user.js)
tools/                      # Build and maintenance scripts
tests/                      # Test suite (Vitest)
```