# **「絵でわかる日本語」Reading Experience Enhancement**

## **Feature Overview**

This script aims to optimize the reading experience on the「絵でわかる日本語」([https://www.edewakaru.com/](https://www.edewakaru.com/)) website.

---

## **Core Features**

### **I. Streamlined Page Layout**

- **Page Purification**: Automatically removes headers, footers, social media buttons, ranking links, and advertisements.
- **Layout Optimization**: Expands the main content area and fixes the sidebar table of contents for easier navigation during long article reading.

### **II. Reading Assistance Features**

- **Automatic Furigana Conversion**: Automatically recognizes and converts "word (pronunciation)" format in articles to standard furigana display. The conversion results have been proofread, but if you find uncovered vocabulary or conversion errors, please submit feedback for continuous improvement.
- **Furigana Toggle**: One-click toggle for the display state of all converted furigana.
- **Text-to-Speech (TTS)**: Select text and click the speech button to use browser's speech synthesis for reading aloud. The system provides both male and female voice options, with random switching for each reading session.

### **III. Performance and Visual Optimization**

- **High-Resolution Image Display**: Automatically replaces blurry thumbnails with high-resolution original images, loading only when images enter the viewport to improve loading speed and visual quality.
- **Lazy Loading of Related Articles**: By default, loads "related articles" and other embedded content only when scrolled nearby. Can be set to "load on click" to reduce network requests.

### **IV. Custom Settings Panel**

A settings panel is provided in the bottom-right corner of the page with the following options:

- **【ページ最適化】**: Master switch for the script; controls the activation status of all features. _(Takes effect after refresh)_
- **【振り仮名表示】**: Controls the display or hiding of furigana. _(Takes effect immediately)_
- **【関連記事表示】**: Sets the loading method for related articles. When enabled, auto-loads; when disabled, loads on click. _(Takes effect after refresh)_
- **【単語選択発音】**: Controls the text-to-speech functionality. <b style="color:#A42121">Only supports Microsoft Edge browser</b>. _(Takes effect immediately)_

---

## **Issue Reporting**

If you encounter any of the following situations, please submit feedback through the Greasy Fork script page:

- Uncovered vocabulary or conversion errors
- Feature malfunctions
- Improvement suggestions

Please provide **the page link (URL) where the issue occurred and a detailed description of the problem** to help us locate and fix issues.

Your feedback is vital support for the continuous improvement of this script. Thank you!
