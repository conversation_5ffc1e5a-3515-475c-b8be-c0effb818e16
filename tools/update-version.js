import fs from 'node:fs'
import path from 'node:path'
import process from 'node:process'
import { fileURLToPath } from 'node:url'
import { promisify } from 'node:util'

// 获取当前文件路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 关键修改：定位到上级目录作为根目录
const rootDir = path.join(__dirname, '..')

// 生成 YYYY.MM.DD 格式的版本号
function getDateVersion() {
  const now = new Date()
  return [
    now.getFullYear(),
    (now.getMonth() + 1).toString().padStart(2, '0'),
    now.getDate().toString().padStart(2, '0'),
  ].join('.')
}

// 执行 Git 命令的函数
async function execGitCommand(command) {
  const { exec } = await import('node:child_process')
  const execPromise = promisify(exec)

  try {
    const { stdout, stderr } = await execPromise(command)
    if (stderr && !stderr.includes('warning:')) {
      console.warn('⚠️ Git 警告:', stderr)
    }
    return stdout.trim()
  }
  catch (error) {
    throw new Error(`Git 操作失败: ${error.message}`)
  }
}

// 主逻辑：更新 package.json 版本号
async function updatePackageVersion() {
  const packagePath = path.join(rootDir, 'package.json')

  try {
    // 读取并解析 package.json
    const data = await fs.promises.readFile(packagePath, 'utf8')
    const packageJson = JSON.parse(data)

    // 生成新版本号
    const newVersion = getDateVersion()

    // 检查是否已是最新版本
    if (packageJson.version === newVersion) {
      console.log(`ℹ️ 版本号未变化 (${newVersion})`)
      return
    }

    // 更新版本号
    const oldVersion = packageJson.version
    packageJson.version = newVersion

    // 写回文件
    await fs.promises.writeFile(
      packagePath,
      `${JSON.stringify(packageJson, null, 2)}\n`,
    )

    console.log(`✅ 版本号已更新: ${oldVersion} → ${newVersion}`)

    // 自动提交到 Git
    try {
      console.log('🚀 正在提交到 Git...')

      // 检查 Git 状态
      const gitStatus = await execGitCommand(`git status --porcelain "${packagePath}"`)
      if (!gitStatus) {
        console.log('⚠️ 没有检测到文件变更，跳过 Git 操作')
        return
      }

      // 添加文件
      await execGitCommand(`git add "${packagePath}"`)

      // 提交更改
      await execGitCommand(`git commit -m "chore: release version ${newVersion}"`)

      // 推送更改
      await execGitCommand('git push')

      console.log('📦 已成功提交并推送版本更新')
    }
    catch (gitError) {
      console.warn(`⚠️ ${gitError.message}`)
      console.log('💡 请手动执行 Git 操作:')
      console.log(`   git add "${packagePath}"`)
      console.log('   git commit -m "chore: release new version"')
      console.log('   git push')
    }
  }
  catch (error) {
    console.error('❌ 更新失败:', error.message)
    process.exit(1)
  }
}

// 执行更新
updatePackageVersion()
