/**
 * @vitest-environment jsdom
 */
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { RULES } from '../../src/config/rules'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter._compileStaticRules 方法', () => {
  beforeEach(() => {
    // 重置 RubyConverter 状态
    RubyConverter._registeredWords = new Set()
    RubyConverter._wordBankForRegex = []
    RubyConverter._rubyCache = new Map()
    RubyConverter._htmlPatches = new Map()
    RubyConverter._simpleTextReplacements = new Map()
    RubyConverter.globalRegex = null

    // 模拟 console 方法以便测试
    console.log = vi.fn()
    console.warn = vi.fn()
    console.error = vi.fn()

    // 初始化 RubyConverter 但不调用 compile
    RubyConverter._rules = RULES
  })

  describe('测试 HTML.FULL_REPLACE 规则处理', () => {
    it('应正确处理 HTML.FULL_REPLACE 规则并创建 HTML 补丁', () => {
      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 验证是否创建了 HTML 补丁
      expect(RubyConverter._htmlPatches.size).toBeGreaterThan(0)

      // 验证特定的 HTML.FULL_REPLACE 规则是否被正确处理
      // 使用实际的第一个规则
      const firstRule = RULES.HTML.FULL_REPLACE[0]

      // 检查是否有匹配的 pattern
      const hasPattern = Array.from(RubyConverter._htmlPatches.keys()).some(pattern =>
        pattern.toString().includes(RubyConverter._escapeRegExp(firstRule.pattern)),
      )

      expect(hasPattern).toBe(true)
    })

    it('应从 HTML.FULL_REPLACE 的 replacement 中提取词条', () => {
      // 创建一个带有明确注音格式的测试规则
      const testRule = {
        pattern: '测试规则',
        replacement: '测试（てすと）规则',
      }

      // 临时添加到 RULES
      const originalRules = [...RULES.HTML.FULL_REPLACE]
      RULES.HTML.FULL_REPLACE.push(testRule)

      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 恢复原始规则
      RULES.HTML.FULL_REPLACE = originalRules

      // 验证是否从 replacement 中提取了词条
      expect(RubyConverter._registeredWords.has('测试（てすと）')).toBe(true)
    })
  })

  describe('测试 HTML.MANUAL_MARK 规则处理', () => {
    // it('应正确处理不带 ? 的 HTML.MANUAL_MARK 规则', () => {
    //   // 添加一个不带 ? 的测试规则
    //   const testRule = '测试（てすと）'

    //   // 临时添加到 RULES
    //   const originalRules = [...RULES.HTML.MANUAL_MARK]
    //   RULES.HTML.MANUAL_MARK.push(testRule)

    //   // 执行 _compileStaticRules 方法
    //   RubyConverter._compileStaticRules()

    //   // 恢复原始规则
    //   RULES.HTML.MANUAL_MARK = originalRules

    //   // 验证是否创建了 HTML 补丁
    //   expect(RubyConverter._htmlPatches.size).toBeGreaterThan(0)

    //   // 验证是否注册了词条
    //   expect(RubyConverter._registeredWords.has(testRule)).toBe(true)
    // })

    it('应正确处理带 ? 的 HTML.MANUAL_MARK 规则', () => {
      // 使用实际的带 ? 的规则
      const testRule = '真っ赤（まっ?か）'

      // 确保规则在列表中
      if (!RULES.HTML.MANUAL_MARK.includes(testRule)) {
        RULES.HTML.MANUAL_MARK.push(testRule)
      }

      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 验证是否创建了 HTML 补丁
      expect(RubyConverter._htmlPatches.size).toBeGreaterThan(0)

      // 验证是否注册了词条 - 注意：这里我们检查的是去掉 ? 后的词条
      const cleanPattern = '真っ赤（まっか）'
      expect(RubyConverter._registeredWords.has(cleanPattern)).toBe(true)
    })
  })

  describe('测试 TEXT.MANUAL_MARK 规则处理', () => {
    it('应正确处理 TEXT.MANUAL_MARK 规则', () => {
      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 验证是否注册了词条
      expect(RubyConverter._registeredWords.size).toBeGreaterThan(0)

      // 验证特定的 TEXT.MANUAL_MARK 规则是否被正确处理
      // 从 RULES.TEXT.MANUAL_MARK 中选择一个例子
      const testExample = RULES.TEXT.MANUAL_MARK[10]
      expect(RubyConverter._registeredWords.has(testExample)).toBe(true)
    })
  })

  describe('测试 TEXT.FULL_REPLACE 规则处理', () => {
    it('应正确处理包含 <ruby> 标签的 TEXT.FULL_REPLACE 规则', () => {
      // 创建一个模拟的 TEXT.FULL_REPLACE 规则，包含 <ruby> 标签
      const mockRule = {
        pattern: '测试（てすと）',
        replacement: '<ruby>测试<rt>てすと</rt></ruby>',
      }

      // 临时添加到 RULES
      const originalRules = [...RULES.TEXT.FULL_REPLACE]
      RULES.TEXT.FULL_REPLACE.push(mockRule)

      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 恢复原始规则
      RULES.TEXT.FULL_REPLACE = originalRules

      // 验证是否注册了词条
      expect(RubyConverter._registeredWords.has('测试（てすと）')).toBe(true)

      // 验证是否存储了替换结果
      expect(RubyConverter._rubyCache.get('测试（てすと）')).toBe('<ruby>测试<rt>てすと</rt></ruby>')
    })

    it('应正确处理不包含 <ruby> 标签的 TEXT.FULL_REPLACE 规则', () => {
      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 验证是否创建了简单文本替换
      expect(RubyConverter._simpleTextReplacements.size).toBeGreaterThan(0)

      // 验证特定的 TEXT.FULL_REPLACE 规则是否被正确处理
      // 从 RULES.TEXT.FULL_REPLACE 中选择一个不包含 <ruby> 标签的例子
      const testRule = RULES.TEXT.FULL_REPLACE.find(rule => !/<ruby[^>]*>/.test(rule.replacement))
      if (testRule) {
        const pattern = RubyConverter._escapeRegExp(testRule.pattern)
        expect(RubyConverter._simpleTextReplacements.has(pattern)).toBe(true)
        expect(RubyConverter._simpleTextReplacements.get(pattern)).toBe(testRule.replacement)
      }
    })
  })

  describe('错误处理', () => {
    it('应处理 HTML.FULL_REPLACE 中的错误', () => {
      // 创建一个会导致错误的规则
      const mockRule = {
        pattern: '错误测试',
        replacement: null, // 这会导致错误
      }

      // 临时添加到 RULES
      const originalRules = [...RULES.HTML.FULL_REPLACE]
      RULES.HTML.FULL_REPLACE.push(mockRule)

      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 恢复原始规则
      RULES.HTML.FULL_REPLACE = originalRules

      // 验证是否记录了错误
      expect(console.error).toHaveBeenCalled()
    })

    it('应处理 TEXT.FULL_REPLACE 中的错误', () => {
      // 创建一个会导致错误的规则
      const mockRule = {
        pattern: '错误测试',
        replacement: null, // 这会导致错误
      }

      // 临时添加到 RULES
      const originalRules = [...RULES.TEXT.FULL_REPLACE]
      RULES.TEXT.FULL_REPLACE.push(mockRule)

      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 恢复原始规则
      RULES.TEXT.FULL_REPLACE = originalRules

      // 验证是否记录了错误
      expect(console.error).toHaveBeenCalled()
    })
  })

  describe('综合功能测试', () => {
    it('应正确构建最终的正则表达式', () => {
      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 构建最终正则
      RubyConverter._buildFinalRegex()

      // 验证是否创建了全局正则
      expect(RubyConverter.globalRegex).not.toBeNull()
      expect(RubyConverter.globalRegex instanceof RegExp).toBe(true)
    })

    it('应正确处理实际的 RULES 配置', () => {
      // 执行 _compileStaticRules 方法
      RubyConverter._compileStaticRules()

      // 验证是否处理了所有规则类型
      expect(RubyConverter._registeredWords.size).toBeGreaterThan(0)
      expect(RubyConverter._htmlPatches.size).toBeGreaterThan(0)
      expect(RubyConverter._simpleTextReplacements.size).toBeGreaterThan(0)
    })
  })
})
