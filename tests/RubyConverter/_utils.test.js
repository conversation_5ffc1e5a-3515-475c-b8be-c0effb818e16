/**
 * @vitest-environment jsdom
 */
import { describe, expect, it } from 'vitest'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter 工具方法', () => {
  // 每个测试前重置状态

  describe('_escapeRegExp 方法', () => {
    it('应该正确转义正则表达式特殊字符', () => {
      const testCases = [
        { input: 'hello.world', expected: 'hello\\.world' },
        { input: 'test(123)', expected: 'test\\(123\\)' },
        { input: '[abc]+', expected: '\\[abc\\]\\+' },
        { input: '漢字(かんじ)', expected: '漢字\\(かんじ\\)' },
        { input: '特殊字符.*+?^${}()|[]\\', expected: '特殊字符\\.\\*\\+\\?\\^\\$\\{\\}\\(\\)\\|\\[\\]\\\\' },
      ]

      testCases.forEach(({ input, expected }) => {
        // 调用 RubyConverter 的私有方法
        const result = RubyConverter._escapeRegExp(input)
        expect(result).toBe(expected)
      })
    })

    it('应该使转义后的字符串在正则表达式中按字面意思匹配', () => {
      const specialChars = '.+*?^$()[]{}|\\'
      const escaped = RubyConverter._escapeRegExp(specialChars)

      // 创建一个使用转义后字符串的正则表达式，添加 ^ 和 $ 锚点确保匹配整个字符串
      const regex = new RegExp(`^${escaped}$`)

      // 应该精确匹配原始字符串
      expect(regex.test(specialChars)).toBe(true)

      // 不应该匹配其他字符串
      expect(regex.test('something else')).toBe(false)
      expect(regex.test(`${specialChars}extra`)).toBe(false)
    })
  })

  describe('_katakanaToHiragana 方法', () => {
    it('应正确将片假名转换为平假名', () => {
      const result = RubyConverter._katakanaToHiragana('テスト')
      expect(result).toBe('てすと')
    })

    it('应正确处理混合片假名和平假名的字符串', () => {
      const result = RubyConverter._katakanaToHiragana('テストです')
      expect(result).toBe('てすとです')
    })

    it('应正确处理包含长音符号的片假名', () => {
      const result = RubyConverter._katakanaToHiragana('コーヒー')
      expect(result).toBe('こーひー')
    })

    it('应正确处理包含非假名字符的字符串', () => {
      const result = RubyConverter._katakanaToHiragana('テスト123')
      expect(result).toBe('てすと123')
    })

    it('应正确处理空字符串', () => {
      const result = RubyConverter._katakanaToHiragana('')
      expect(result).toBe('')
    })

    it('应正确处理null或undefined值', () => {
      expect(RubyConverter._katakanaToHiragana(null)).toBe('')
      expect(RubyConverter._katakanaToHiragana(undefined)).toBe('')
    })
  })

  // 添加对 _regex 对象中正则表达式的测试
  describe('正则表达式工具方法', () => {
    it('应该通过 _regex 对象提供正则表达式功能', () => {
      // 检查 _regex 对象是否存在
      expect(RubyConverter._regex).toBeDefined()

      // 检查一些关键的正则表达式是否存在
      expect(RubyConverter._regex.hasOnlyKana).toBeDefined()
      expect(RubyConverter._regex.hasOnlyHiragana).toBeDefined()
      expect(RubyConverter._regex.hasOnlyKatakana).toBeDefined()
      expect(RubyConverter._regex.hasKanaChar).toBeDefined()
      expect(RubyConverter._regex.hasOnlyKanjiKana).toBeDefined()
    })
  })
})
