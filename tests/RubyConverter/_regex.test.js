import { describe, expect } from 'vitest'
import RubyConverter from '../../src/modules/RubyConverter'

describe('rubyConverter 正则表达式测试', () => {
  // 获取 RubyConverter 中的正则表达式
  const regex = RubyConverter._regex

  describe('matchBracketRuby', () => {
    it('应该匹配【】或「」括号中的注音格式', () => {
      const testCases = [
        {
          input: '这是一个【测试（てすと）】例子',
          expected: ['【测试（てすと）】'],
          groups: [['测试', 'てすと', '']],
        },
        {
          input: '这是「汉字（かんじ）」和【日本語（にほんご）】',
          expected: ['「汉字（かんじ）」', '【日本語（にほんご）】'],
          groups: [['汉字', 'かんじ', ''], ['日本語', 'にほんご', '']],
        },
        {
          input: '没有匹配的内容',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchBracketRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 汉字部分
            expect(match[2]).toBe(groups[i][1]) // 读音部分
            expect(match[3]).toBe(groups[i][2]) // 后缀部分
          }
        })
      })
    })
  })

  describe('matchKanjiRuby', () => {
    it('应该匹配汉字后跟括号中的读音', () => {
      const testCases = [
        {
          input: '漢字(かんじ)の例',
          expected: ['漢字(かんじ)'],
          groups: [['漢字', 'かんじ']],
        },
        {
          input: '日本語（にほんご）と中国語（ちゅうごくご）',
          expected: ['日本語（にほんご）', '中国語（ちゅうごくご）'],
          groups: [['日本語', 'にほんご'], ['中国語', 'ちゅうごくご']],
        },
        {
          input: '漢字だけ',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchKanjiRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 汉字部分
            expect(match[2]).toBe(groups[i][1]) // 读音部分
          }
        })
      })
    })
  })

  describe('matchLoanwordRuby', () => {
    it('应该匹配英文后跟括号中的片假名', () => {
      const testCases = [
        {
          input: '１００m（メートル）',
          expected: ['m（メートル）'],
          groups: [['m', 'メートル']],
        },
        {
          input: 'OL（オーエル）',
          expected: ['OL（オーエル）'],
          groups: [['OL', 'オーエル']],
        },
        {
          input: 'Owhite shirt（ホワイトシャツ）',
          expected: ['Owhite shirt（ホワイトシャツ）'],
          groups: [['Owhite shirt', 'ホワイトシャツ']],
        },
        {
          input: '春（ピンク）',
          expected: [],
          groups: [],
        },
        {
          input: '英語なし',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchLoanwordRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1].toLowerCase()).toBe(groups[i][0].toLowerCase()) // 英文部分
            expect(match[2]).toBe(groups[i][1]) // 片假名部分
          }
        })
      })
    })
  })

  describe('matchKatakanaRuby', () => {
    it('应该匹配片假名后跟括号中的英文', () => {
      const testCases = [
        {
          input: 'エアーコンディショナー（air conditioner）',
          expected: ['エアーコンディショナー（air conditioner）'],
          groups: [['エアーコンディショナー', 'air conditioner']],
        },
        {
          input: 'サイバーマンデー（Cyber Monday）',
          expected: ['サイバーマンデー（Cyber Monday）'],
          groups: [['サイバーマンデー', 'Cyber Monday']],
        },
        {
          input: 'ランニングマシーン（running＋machine）',
          expected: ['ランニングマシーン（running＋machine）'],
          groups: [['ランニングマシーン', 'running＋machine']],
        },
        {
          input: 'オムライス（omelet+rice）',
          expected: ['オムライス（omelet+rice）'],
          groups: [['オムライス', 'omelet+rice']],
        },
        {
          input: 'サボ（さぼ）る',
          expected: [],
          groups: [],
        },
        {
          input: 'カタカナなし',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.matchKatakanaRuby))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 片假名部分
            expect(match[2].toLowerCase()).toBe(groups[i][1].toLowerCase()) // 英文部分
          }
        })
      })
    })
  })

  describe('测试 ExtractCandidates', () => {
    it('应该提取非空白、非标点、非尖括号的文本与其读音', () => {
      const testCases = [
        {
          input: '漢字（かんじ）の例',
          expected: ['漢字（かんじ）'],
          groups: [['漢字', 'かんじ']],
        },
        {
          input: '日本語（にほんご）と中国語（ちゅうごくご）',
          expected: ['日本語（にほんご）', 'と中国語（ちゅうごくご）'],
          groups: [['日本語', 'にほんご'], ['と中国語', 'ちゅうごくご']],
        },
        {
          input: '空白 （くうはく）は除外',
          expected: [],
          groups: [],
        },
        {
          input: '<div>（てすと）は除外</div>',
          expected: [],
          groups: [],
        },
      ]

      testCases.forEach(({ input, expected, groups }) => {
        const matches = Array.from(input.matchAll(regex.extractCandidates))
        expect(matches.map(m => m[0])).toEqual(expected)

        matches.forEach((match, i) => {
          if (groups[i]) {
            expect(match[1]).toBe(groups[i][0]) // 文本部分
            expect(match[2]).toBe(groups[i][1]) // 读音部分
          }
        })
      })
    })
  })

  describe('测试 ExtractKanjiAndReading & extractReading', () => {
    it('extractKanjiAndReading 应该提取汉字和读音', () => {
      const testCases = [
        {
          input: '漢字（かんじ）',
          expected: ['漢字（かんじ）', '漢字', 'かんじ'],
        },
        {
          input: '日本語（にほんご）',
          expected: ['日本語（にほんご）', '日本語', 'にほんご'],
        },
        {
          input: '没有括号',
          expected: null,
        },
      ]

      testCases.forEach(({ input, expected }) => {
        const match = input.match(regex.extractKanjiAndReading)
        if (expected === null) {
          expect(match).toBeNull()
        }
        else {
          // 只检查前三个元素，忽略其他可能的捕获组
          if (match) {
            expect(match.slice(0, 3)).toEqual(expected)
          }
          else {
            expect(match).toEqual(expected)
          }
        }
      })
    })

    it('extractReading 应该只提取读音部分', () => {
      const testCases = [
        {
          input: '漢字（かんじ）',
          expected: ['（かんじ）', 'かんじ'],
        },
        {
          input: '日本語（にほんご）',
          expected: ['（にほんご）', 'にほんご'],
        },
        {
          input: '没有括号',
          expected: null,
        },
      ]

      testCases.forEach(({ input, expected }) => {
        const match = input.match(regex.extractReading)
        if (expected === null) {
          expect(match).toBeNull()
        }
        else {
          // 只检查前两个元素，忽略其他可能的捕获组
          if (match) {
            expect(match.slice(0, 2)).toEqual(expected)
          }
          else {
            expect(match).toEqual(expected)
          }
        }
      })
    })
  })

  describe('测试 testHtmlTag & testRubyTag', () => {
    it('isHtmlTag 应该匹配 HTML 标签', () => {
      // 由于在测试环境中，我们需要重置正则表达式的 lastIndex
      const resetAndTest = (regex, str) => {
        regex.lastIndex = 0
        return regex.test(str)
      }

      expect(resetAndTest(regex.isHtmlTag, '<div>')).toBe(true)
      expect(resetAndTest(regex.isHtmlTag, '<span class="test">')).toBe(true)
      expect(resetAndTest(regex.isHtmlTag, '</div>')).toBe(true)
      expect(resetAndTest(regex.isHtmlTag, '<br />')).toBe(true)
      expect(resetAndTest(regex.isHtmlTag, '普通文本')).toBe(false)
    })

    it('isRubyTag 应该匹配 ruby 标签', () => {
      expect(regex.isRubyTag.test('<ruby>')).toBe(true)
      expect(regex.isRubyTag.test('<ruby class="test">')).toBe(true)
      expect(regex.isRubyTag.test('<div>')).toBe(false)
      expect(regex.isRubyTag.test('</ruby>')).toBe(false)
    })
  })

  describe('测试 hasOnlyKana 相关正则', () => {
    it('hasOnlyKana 应该检测是否只包含假名字符', () => {
      expect(regex.hasOnlyKana.test('あいうえお')).toBe(true)
      expect(regex.hasOnlyKana.test('アイウエオ')).toBe(true)
      expect(regex.hasOnlyKana.test('あア')).toBe(true)
      expect(regex.hasOnlyKana.test('アニメー')).toBe(true) // 包含长音符号
      expect(regex.hasOnlyKana.test('ラーメン')).toBe(true) // 包含长音符号
      expect(regex.hasOnlyKana.test('漢字')).toBe(false)
      expect(regex.hasOnlyKana.test('abc')).toBe(false)
      expect(regex.hasOnlyKana.test('あa')).toBe(false)
      expect(regex.hasOnlyKana.test('あ〇')).toBe(false) // 特殊符号〇
      expect(regex.hasOnlyKana.test('あ〜')).toBe(false) // 特殊符号〜
      expect(regex.hasOnlyKana.test('あ・')).toBe(true) // 特殊符号・
      expect(regex.hasOnlyKana.test('あ１')).toBe(false) // 全角数字１
      expect(regex.hasOnlyKana.test('あ6')).toBe(false) // 半角数字6
    })

    it('hasOnlyHiragana 应该检测是否只包含平假名字符', () => {
      expect(regex.hasOnlyHiragana.test('あいうえお')).toBe(true)
      expect(regex.hasOnlyHiragana.test('アイウエオ')).toBe(false)
      expect(regex.hasOnlyHiragana.test('あア')).toBe(false)
      expect(regex.hasOnlyHiragana.test('漢字')).toBe(false)
      expect(regex.hasOnlyHiragana.test('abc')).toBe(false)
      expect(regex.hasOnlyHiragana.test('あa')).toBe(false)
      expect(regex.hasOnlyHiragana.test('あ〇')).toBe(false) // 特殊符号〇
      expect(regex.hasOnlyHiragana.test('あ〜')).toBe(false) // 特殊符号〜
      expect(regex.hasOnlyHiragana.test('あ・')).toBe(false) // 特殊符号・
      expect(regex.hasOnlyHiragana.test('あ１')).toBe(false) // 全角数字１
      expect(regex.hasOnlyHiragana.test('あ6')).toBe(false) // 半角数字6
    })

    it('hasOnlyKatakana 应该检测是否只包含片假名字符', () => {
      expect(regex.hasOnlyKatakana.test('あいうえお')).toBe(false)
      expect(regex.hasOnlyKatakana.test('アイウエオ')).toBe(true)
      expect(regex.hasOnlyKatakana.test('アニメー')).toBe(true) // 包含长音符号
      expect(regex.hasOnlyKatakana.test('ラーメン')).toBe(true) // 包含长音符号
      expect(regex.hasOnlyKatakana.test('ー')).toBe(true) // 单独的长音符号
      expect(regex.hasOnlyKatakana.test('あア')).toBe(false)
      expect(regex.hasOnlyKatakana.test('漢字')).toBe(false)
      expect(regex.hasOnlyKatakana.test('abc')).toBe(false)
      expect(regex.hasOnlyKatakana.test('アa')).toBe(false)
      expect(regex.hasOnlyKatakana.test('ア〇')).toBe(false) // 特殊符号〇
      expect(regex.hasOnlyKatakana.test('ア〜')).toBe(false) // 特殊符号〜
      expect(regex.hasOnlyKatakana.test('ア・')).toBe(true) // 特殊符号・
      expect(regex.hasOnlyKatakana.test('ア１')).toBe(false) // 全角数字１
      expect(regex.hasOnlyKatakana.test('ア6')).toBe(false) // 半角数字6
    })

    it('hasKanaChar 应该检测是否为单个假名字符', () => {
      expect(regex.hasKanaChar.test('あ')).toBe(true)
      expect(regex.hasKanaChar.test('ア')).toBe(true)
      expect(regex.hasKanaChar.test('ー')).toBe(true) // 长音符号
      expect(regex.hasKanaChar.test('漢')).toBe(false)
      expect(regex.hasKanaChar.test('a')).toBe(false)
      expect(regex.hasKanaChar.test('あア')).toBe(false) // 多个字符
      expect(regex.hasKanaChar.test('')).toBe(false) // 空字符串
      expect(regex.hasKanaChar.test('〇')).toBe(false) // 特殊符号〇
      expect(regex.hasKanaChar.test('〜')).toBe(false) // 特殊符号〜
      expect(regex.hasKanaChar.test('・')).toBe(true) // 特殊符号・
      expect(regex.hasKanaChar.test('１')).toBe(false) // 全角数字１
      expect(regex.hasKanaChar.test('6')).toBe(false) // 半角数字6
    })
  })

  describe('测试 hasOnlyKanjiKana', () => {
    it('hasOnlyKanjiKana 应该检测是否只包含汉字和假名字符', () => {
      expect(regex.hasOnlyKanjiKana.test('あいうえお')).toBe(true)
      expect(regex.hasOnlyKanjiKana.test('アイウエオ')).toBe(true)
      expect(regex.hasOnlyKanjiKana.test('アニメー')).toBe(true) // 包含长音符号
      expect(regex.hasOnlyKanjiKana.test('ラーメン')).toBe(true) // 包含长音符号
      expect(regex.hasOnlyKanjiKana.test('漢字')).toBe(true)
      expect(regex.hasOnlyKanjiKana.test('あア漢字')).toBe(true)
      expect(regex.hasOnlyKanjiKana.test('abc')).toBe(false)
      expect(regex.hasOnlyKanjiKana.test('あa')).toBe(false)
      expect(regex.hasOnlyKanjiKana.test('漢字123')).toBe(false)
      expect(regex.hasOnlyKanjiKana.test('アイウエオ!')).toBe(false)
      expect(regex.hasOnlyKanjiKana.test('漢字〇')).toBe(false) // 特殊符号〇
      expect(regex.hasOnlyKanjiKana.test('漢字〜')).toBe(false) // 特殊符号〜
      expect(regex.hasOnlyKanjiKana.test('漢字・')).toBe(true) // 特殊符号・
      expect(regex.hasOnlyKanjiKana.test('漢字１')).toBe(false) // 全角数字１
      expect(regex.hasOnlyKanjiKana.test('漢字6')).toBe(false) // 半角数字6
    })

    it('应该正确处理特殊汉字和罕见字符', () => {
      expect(regex.hasOnlyKanjiKana.test('々')).toBe(true) // 汉字重复符号
      expect(regex.hasOnlyKanjiKana.test('漢々')).toBe(true) // 带重复符号的汉字
      expect(regex.hasOnlyKanjiKana.test('ヵ')).toBe(true) // 小型片假名「カ」
      expect(regex.hasOnlyKanjiKana.test('ヶ')).toBe(true) // 小型片假名「ケ」
      expect(regex.hasOnlyKanjiKana.test('〆')).toBe(false) // 特殊符号〆(しめ)
      expect(regex.hasOnlyKanjiKana.test('〒')).toBe(false) // 邮编符号
    })
  })
})
